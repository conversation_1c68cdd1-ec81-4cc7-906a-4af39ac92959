import router from './router'
import { useTitle } from '@/hooks/web/useTitle'
import { useNProgress } from '@/hooks/web/useNProgress'
import { usePermissionStoreWithOut } from '@/store/modules/permission'
import { usePageLoading } from '@/hooks/web/usePageLoading'
import { NO_REDIRECT_WHITE_LIST } from '@/constants'
import { useStorage } from '@/hooks/web/useStorage'
import { useUserStoreWithOut } from './store/modules/user'
import { useDictStore } from './store/modules/dict'

const { getStorage } = useStorage('localStorage')
const { start, done } = useNProgress()

const { loadStart, loadDone } = usePageLoading()
const title = import.meta.env.VITE_APP_TITLE

router.beforeEach(async (to, from, next) => {
  start()
  loadStart()

  const permissionStore = usePermissionStoreWithOut()
  // const appStore = useAppStoreWithOut()
  const userStore = useUserStoreWithOut()
  const dictStore = useDictStore()

  const token = getStorage('token')
  if (token) {
    if (to.path === '/login') {
      next({ path: '/' })
    } else {
      if (!permissionStore.getIsAddRouters) {
        await Promise.all([
          permissionStore.generateMenuRoute(),
          userStore.requestUserInfo(),
          dictStore.fetchCountryList(),
          dictStore.fetchLanguageList(),
          dictStore.fetchLanguageItemList()
        ])

        const redirectPath = from.query.redirect || to.path
        const redirect = decodeURIComponent(redirectPath as string)
        const nextData = to.path === redirect ? { ...to, replace: true } : { path: redirect }
        next(nextData)
      } else {
        next()
      }
    }
  } else {
    if (NO_REDIRECT_WHITE_LIST.indexOf(to.path) !== -1) {
      next()
    } else {
      next(`/login?redirect=${to.path}`) // 否则全部重定向到登录页
    }
  }
})

router.afterEach((to) => {
  useTitle(to?.meta?.title as string)
  done() // 结束Progress
  loadDone()
})
