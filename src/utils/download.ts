import { downloadFile } from '@/api/search'

export async function downloadRes(params) {
  const response = (await downloadFile(params)) as any

  // 创建 Blob 对象
  const blob = new Blob([response.data])
  const url = window.URL.createObjectURL(blob)
  const link = document.createElement('a')
  link.href = url

  // 解析 Content-Disposition 获取文件名
  const contentDisposition = response?.headers['content-disposition']
  let fileName = 'downloaded_file'

  if (contentDisposition) {
    // 处理 UTF-8 编码的文件名
    // 匹配 filename*=UTF-8''xxxxxx 格式
    const matches = contentDisposition.match(/filename\*=UTF-8''([^;]+)/i)
    if (matches && matches[1]) {
      // 解码 URL 编码的文件名
      fileName = decodeURIComponent(matches[1])
    } else {
      // 兼容处理普通 filename="xxx" 格式
      const filenameMatch = contentDisposition.match(/filename="?([^"]+)"?/)
      if (filenameMatch && filenameMatch[1]) {
        fileName = filenameMatch[1]
      }
    }
  }

  link.setAttribute('download', fileName)
  document.body.appendChild(link)
  link.click()

  // 清理
  window.URL.revokeObjectURL(url)
  link.remove()
}
