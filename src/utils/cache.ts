import { getCountry<PERSON><PERSON>, getLanguagesApi, getLanguageItemsApi } from '@/api/home'
// import { useStorage } from '@/hooks/web/useStorage'
// const { getStorage, setStorage } = useStorage('localStorage')

export const getCountryList = async () => {
  const { data } = await getCountryApi()
  return data
}

export const getLanguageList = async () => {
  const { data } = await getLanguagesApi()
  return data
}

export const getLanguageItemList = async () => {
  const { data } = await getLanguageItemsApi()
  return data
}
