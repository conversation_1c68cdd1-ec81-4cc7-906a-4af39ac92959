import { Push } from '@/utils/push-vue.js'

export const getPushConnection = () => {
  // 建立连接
  var connection = new Push({
    url:
      (window.location.protocol === 'https:' ? 'wss://' : 'ws://') +
      window.location.hostname +
      ':3131', // websocket地址
    app_key: import.meta.env.VITE_PUSHER_APP_KEY,
    auth: import.meta.env.VITE_PUSHER_BASE_URL + '/plugin/webman/push/auth'
  })

  return connection
}
