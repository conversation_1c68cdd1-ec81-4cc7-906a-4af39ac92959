import { useDictStore } from '@/store/modules/dict'

const dictStore = useDictStore()

export const filterIsFinished = (isFinished, isColor = false) => {
  const typeMap = {
    0: isColor ? 'warning' : '否',
    1: isColor ? 'success' : '是',
    2: isColor ? 'danger' : '异常'
  }
  return typeMap[isFinished]
}

export const filterType = (type, isColor = false) => {
  const typeMapping = {
    1: isColor ? 'success' : '精确搜索',
    2: isColor ? 'info' : '前缀搜索',
    3: isColor ? 'warning' : '后缀搜索',
    4: isColor ? 'danger' : '模糊搜索',
    5: isColor ? 'primary' : '模糊搜索'
  }
  return typeMapping[type]
}

export const filterStatus = (type, isColor = false) => {
  const typeMapping = {
    0: isColor ? 'primary' : '等待执行',
    1: isColor ? 'info' : '执行中',
    2: isColor ? 'success' : '已完成',
    3: isColor ? 'danger' : '异常'
  }
  return typeMapping[type]
}

export const filterExportFileStatus = (type, isColor = false) => {
  const typeMapping = {
    1: isColor ? 'info' : '执行中',
    2: isColor ? 'success' : '已完成'
  }
  return typeMapping[type]
}

export const filterSource = (type, isColor = false) => {
  const typeMapping = {
    1: isColor ? 'success' : '普通搜索',
    2: isColor ? 'warning' : '任务布控'
  }
  return typeMapping[type]
}

export const filterExecType = (type, isColor = false) => {
  const typeMapping = {
    1: isColor ? 'success' : '延迟执行',
    2: isColor ? 'warning' : '立即执行'
  }
  return typeMapping[type]
}

export const filterSearchRecordType = (type, isColor = false) => {
  const typeMapping = {
    1: isColor ? 'success' : '普通搜索',
    2: isColor ? 'warning' : '密码搜索',
    3: isColor ? 'info' : '文件搜索'
  }
  return typeMapping[type]
}

export const filterAutoLoginStatus = (type, isColor = false) => {
  const typeMapping = {
    1: isColor ? 'warning' : '执行中',
    2: isColor ? 'success' : '已完成'
  }
  return typeMapping[type]
}

export const filterExportFileSource = (type, isColor = false) => {
  const typeMapping = {
    1: isColor ? 'success' : '密码搜索'
  }
  return typeMapping[type]
}

export const filterCountry = (country) => {
  const countryList = dictStore.getCountryList
  const current = countryList.find((item) => item.code === country)
  return current ? current.name : country
}

export const filterLanguage = (language) => {
  // return language
  const languageItemList = dictStore.getLanguageItemList
  const current = languageItemList.find((item) => item.code === language)
  return current ? (current.language ? current.language.name : language) : language
}
