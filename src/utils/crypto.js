import CryptoJS from 'crypto-js'

export const encryptString = (string) => {
  // 使用与 PHP 端相同的 key 和 iv
  const envKey = import.meta.env.VITE_PASSWORD_KEY
  const envIv = import.meta.env.VITE_PASSWORD_IV

  const key = CryptoJS.enc.Utf8.parse(envKey) // 替换为你的 key
  const iv = CryptoJS.enc.Utf8.parse(envIv) // 替换为你的 iv

  // 加密
  const encrypted = CryptoJS.AES.encrypt(string, key, {
    iv: iv,
    mode: CryptoJS.mode.CBC,
    padding: CryptoJS.pad.Pkcs7
  })

  // 转换为 Base64 格式
  return encrypted.toString()
}
