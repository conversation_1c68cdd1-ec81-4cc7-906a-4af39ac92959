import request from '@/axios'

export const getProxyListApi = (params) => {
  return request.get({ url: '/ai/proxy/list', params })
}

export const getAiAttackListApi = (params) => {
  return request.get({ url: '/ai/attack', params })
}

export const postAiAttackApi = (data) => {
  return request.post({ url: '/ai/attack', data })
}

export const getAiAttackDetailApi = (id, params) => {
  return request.get({ url: `/ai/attack/${id}`, params })
}

export const putAiAttackApi = (id, data) => {
  return request.put({ url: `/ai/attack/${id}`, data })
}
