import request from '@/axios'

export const getExportFileListApi = (params) => {
  return request.get({ url: '/exportFile', params })
}

export const downloadFileApi = (id) => {
  return request.get({ url: `/exportFile/${id}/download`, responseType: 'blob' })
}

export const checkExcelApi = (id) => {
  return request.get({ url: `/exportFile/${id}/checkExcel` })
}

export const deleteDownloadApi = (id) => {
  return request.delete({ url: `/exportFile/${id}` })
}
