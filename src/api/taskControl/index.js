import request from '@/axios'

export const getTaskControlListApi = (params) => {
  return request.get({ url: '/taskControl', params })
}

export const createTaskControlApi = (data) => {
  return request.post({ url: '/taskControl', data })
}

export const updateTaskControlApi = (id, data) => {
  return request.put({ url: `/taskControl/${id}`, data })
}

export const getTaskControlTasksApi = (params) => {
  return request.get({ url: '/taskControl/task/list', params })
}

export const deleteTaskControlApi = (id) => {
  return request.delete({ url: `/taskControl/${id}` })
}

export const resetPoolApi = () => {
  return request.delete({ url: '/taskControl/reset/pool' })
}

export const downloadTaskControlApi = (params) => {
  return request.get({ url: `/taskControl/task/list/export`, params, responseType: 'blob' })
}
