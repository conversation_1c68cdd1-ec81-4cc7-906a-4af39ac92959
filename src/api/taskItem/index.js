import request from '@/axios'

export const getTaskItemListApi = (params) => {
  return request.get({ url: '/taskItem', params })
}

// export const createMultipleTaskApi = (data) => {
//   return request.post({ url: '/task/createMultiple', data })
// }

export const getTaskItemDetailApi = (id) => {
  return request.get({ url: `/taskItem/${id}` })
}

export const deleteTaskItemApi = (id) => {
  return request.delete({ url: `/taskItem/${id}` })
}

export const putTaskItemApi = (id, data) => {
  return request.put({ url: `/taskItem/${id}`, data })
}

export const getTaskPoolApi = () => {
  return request.get({ url: `/task/current/pool` })
}

export const deleteTaskItemByIdApi = (params) => {
  return request.delete({ url: '/taskItem/batch/delete', data: { ids: params } })
}

export const clearTaskItemApi = (params) => {
  return request.delete({ url: '/taskItem/clearData/all', data: params })
}
