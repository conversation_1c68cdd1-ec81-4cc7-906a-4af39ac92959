import request from '@/axios'
import { TokenType } from '../login/types'

export const loginApi = (data): Promise<IResponse<TokenType>> => {
  return request.post({ url: '/auth/login', data })
}

export const getUserinfoApi = () => {
  return request.get({ url: '/auth/me' })
}

export const getMyPointAPI = () => {
  return request.get({ url: '/auth/myPoint' })
}

export const updateUserinfoApi = (params) => {
  return request.put({ url: '/auth/me', data: params })
}

export const updatePasswordApi = (params) => {
  return request.put({ url: '/auth/password/update', data: params })
}

export const getCaptcha = () => {
  return request.get({ url: '/auth/captcha' })
}
