import request from '@/axios'

export const getApiRouteListApi = (params) => {
  return request.get({ url: '/apiRoute', params })
}

export const createApiRouteApi = (params) => {
  return request.post({ url: '/apiRoute', data: params })
}

export const updateApiRouteApi = (id, params) => {
  return request.put({ url: `/apiRoute/${id}`, data: params })
}

export const deleteApiRouteByIdApi = (params) => {
  return request.delete({ url: '/apiRoute', data: { ids: params } })
}
