import request from '@/axios'

export const getResultListApi = (params) => {
  return request.get({ url: '/es', params })
}

export const getNewResultListApi = (params) => {
  return request.get({ url: '/es/toSearch', params })
}

export const getCidListApi = (params) => {
  return request.get({ url: '/es/cid', params })
}

export const getSearchScheduleAPI = (params) => {
  return request.get({ url: '/es/searchSchedule', params })
}

export const getSearchModulesAPI = (params) => {
  return request.get({ url: '/es/searchModules', params })
}

export const getSearchCountryAPI = (params) => {
  return request.get({ url: '/es/searchCountry', params })
}

export const getSearchLanguageAPI = (params) => {
  return request.get({ url: '/es/searchLanguage', params })
}
