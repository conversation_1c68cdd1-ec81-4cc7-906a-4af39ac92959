import request from '@/axios'

export const getPasswordListAPI = (params) => {
  return request.get({ url: '/password/search', params })
}

export const exportPasswordListAPI = (params) => {
  return request.get({ url: '/password/export', params, responseType: 'blob' })
}

export const checkPasswordAPI = (params) => {
  return request.post({ url: '/password/checkPassword', data: params })
}

export const getPasswordAPI = (params) => {
  return request.post({ url: '/password/getPassword', data: params })
}
