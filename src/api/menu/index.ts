import request from '@/axios'

export const getMenuListApi = (params) => {
  return request.get({ url: '/menu', params })
}

export const getMenuRouteApi = () => {
  return request.get({ url: '/menu/route' })
}

export const createMenuApi = (params) => {
  return request.post({ url: '/menu', data: params })
}

export const updateMenuApi = (id, params) => {
  return request.put({ url: `/menu/${id}`, data: params })
}

export const deleteMenuApi = (id) => {
  return request.delete({ url: `/menu/${id}` })
}

export const setMenuApiRouteApi = (id, params) => {
  return request.put({ url: `/menu/${id}/apiRoutes`, data: params })
}

export const getApiRouteIdsApi = (id) => {
  return request.get({ url: `/menu/${id}/apiRoutes` })
}
