import request from '@/axios'

export const getRoleListApi = (params) => {
  return request.get({ url: '/role', params })
}

export const getAuthRoleApi = () => {
  return request.get({ url: '/authRole' })
}

export const createRoleApi = (params) => {
  return request.post({ url: '/role', data: params })
}

export const updateRoleApi = (id, params) => {
  return request.put({ url: `/role/${id}`, data: params })
}

export const deleteRoleByIdApi = (params) => {
  return request.delete({ url: '/role', data: { ids: params } })
}
