import request from '@/axios'

export const getTaskListApi = (params) => {
  return request.get({ url: '/task', params })
}

export const createMultipleTaskApi = (data) => {
  return request.post({ url: '/task/createMultiple', data })
}

export const getTaskDetailApi = (id) => {
  return request.get({ url: `/task/${id}` })
}

export const deleteTaskApi = (id) => {
  return request.delete({ url: `/task/${id}` })
}

export const putTaskApi = (id, data) => {
  return request.put({ url: `/task/${id}`, data })
}

export const getTaskPoolApi = () => {
  return request.get({ url: `/task/current/pool` })
}

export const deleteTaskByIdApi = (params) => {
  return request.delete({ url: '/task/batch/delete', data: { ids: params } })
}
