import request from '@/axios'

export const searchSysinfoAPI = (params) => {
  return request.get({ url: '/search/sysinfo', params })
}

export const searchAccountAPI = (params) => {
  return request.get({ url: '/search/account', params })
}

export const searchContentAPI = (params) => {
  return request.get({ url: '/search/content', params })
}

export const searchItemAPI = (params) => {
  return request.get({ url: '/search/item', params })
}

export const searchPasswordResultAPI = (params) => {
  return request.get({ url: '/search/passwordResult', params })
}

export const searchCountAPI = (params) => {
  return request.get({ url: '/search/count', params })
}

export const downloadFile = (params) => {
  return request.get({ url: '/file/download', params, responseType: 'blob' })
}

export const searchCountryAPI = (params) => {
  return request.get({ url: '/search/country', params })
}

export const checkCidAuthFromPageApi = (params) => {
  return request.get({ url: '/checkCidAuthFromPage', params })
}

export const getMatchModulesApi = (cid, taskId) => {
  return request.get({ url: `/search/${cid}/matchModules/${taskId}` })
}
