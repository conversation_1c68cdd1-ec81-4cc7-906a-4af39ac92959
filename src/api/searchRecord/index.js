import request from '@/axios'

export const createSearchRecordApi = (params) => {
  return request.post({ url: '/searchRecord', data: params })
}

export const getSearchRecordApi = (params) => {
  return request.get({ url: '/searchRecord', params })
}

export const getRecentlySearchRecordApi = () => {
  return request.get({ url: '/searchRecord/recently/list' })
}

export const deleteRecentlySearchRecordApi = () => {
  return request.delete({ url: '/searchRecord/clear/all' })
}
