import request from '@/axios'

export const getUserListApi = (params) => {
  return request.get({ url: '/user', params })
}

export const createUserApi = (data) => {
  return request.post({ url: '/user', data })
}

export const updateUserApi = (id, params) => {
  return request.put({ url: `/user/${id}`, data: params })
}

export const deleteUserByIdApi = (params) => {
  return request.delete({ url: '/user', data: { ids: params } })
}

export const unLockUserApi = (id: number | string) => {
  return request.put({ url: `/user/${id}/unLock` })
}

export const updateUserStatusApi = (id: number | string, status: number | string) => {
  return request.put({ url: `/user/${id}/status`, data: { status } })
}

export const getAuthListAPI = () => {
  return request.get({ url: `/user/auth/list ` })
}

export const exportUserApi = (params) => {
  return request.get({ url: `/user/export/all`, params, responseType: 'blob' })
}
