import { defineStore } from 'pinia'
import { store } from '../index'
import { ElMessageBox } from 'element-plus'
import { useI18n } from '@/hooks/web/useI18n'
import { useTagsViewStore } from './tagsView'
import router from '@/router'
import { useStorage } from '@/hooks/web/useStorage'
import { getUserinfoApi, loginApi } from '@/api/auth'

import { usePermissionStore } from './permission'

const { getStorage, setStorage, removeStorage } = useStorage('localStorage')

export const useUserStore = defineStore('user', {
  state: () => {
    return {
      userInfo: null,
      tokenKey: 'Authorization',
      token: ''
    }
  },
  getters: {
    getTokenKey(): string {
      return this.tokenKey
    },
    getToken(): string {
      return this.token || getStorage('token')
    },
    getUserInfo(): any {
      return this.userInfo
    }
  },
  actions: {
    setTokenKey(tokenKey: string) {
      this.tokenKey = tokenKey
    },
    setToken(token: string) {
      setStorage('token', token)
      this.token = token
    },
    clearToken() {
      removeStorage('token')
    },
    setUserInfo(userInfo) {
      this.userInfo = userInfo
    },
    logoutConfirm() {
      const { t } = useI18n()
      ElMessageBox.confirm(t('common.loginOutMessage'), t('common.reminder'), {
        confirmButtonText: t('common.ok'),
        cancelButtonText: t('common.cancel'),
        type: 'warning'
      })
        .then(async () => {
          this.reset()
        })
        .catch(() => {})
    },
    reset() {
      const tagsViewStore = useTagsViewStore()
      tagsViewStore.delAllViews()
      this.clearToken()
      this.setUserInfo(null)

      const permissionStore = usePermissionStore()
      permissionStore.setIsAddRouters(false)

      router.replace({
        path: '/login'
      })
    },
    logout() {
      this.reset()
    },
    async requestLogin(formatData) {
      const tokenRes = await loginApi(formatData)

      if (tokenRes) {
        const token = `${tokenRes.data.token_type} ${tokenRes.data.access_token}`
        // 触发 storage 事件
        const storageEvent = new StorageEvent('storage', {
          key: 'token',
          newValue: token
        })
        window.dispatchEvent(storageEvent)

        this.setToken(token)
      }
    },
    async requestUserInfo() {
      const userRes = await getUserinfoApi()
      this.setUserInfo(userRes.data)
    }
  }
})

export const useUserStoreWithOut = () => {
  return useUserStore(store)
}
