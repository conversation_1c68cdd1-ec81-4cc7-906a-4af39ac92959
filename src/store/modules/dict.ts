import { defineStore } from 'pinia'
import { store } from '../index'
import { getCountryApi, getLanguagesApi, getLanguageItemsApi } from '@/api/home'

export const useDictStore = defineStore('dict', {
  state: () => {
    return {
      countryList: [],
      languageList: [],
      languageItemList: []
    }
  },
  persist: false, // 这个store不需要持久化存储，
  getters: {
    getCountryList(): any {
      return this.countryList
    },
    getLanguageList(): any {
      return this.languageList
    },
    getLanguageItemList(): any {
      return this.languageItemList
    }
  },
  actions: {
    setCountryList(countryList) {
      this.countryList = countryList
    },
    setLanguageList(languageList) {
      this.languageList = languageList
    },
    setLanguageItemList(languageItemList) {
      this.languageItemList = languageItemList
    },
    // 新增方法：获取国家列表，如果 store 中没有数据则从 API 获取
    async fetchCountryList() {
      if (this.countryList.length === 0) {
        const { data } = await getCountryApi()
        this.setCountryList(data)
      }
      return this.countryList
    },

    // 新增方法：获取语言列表，如果 store 中没有数据则从 API 获取
    async fetchLanguageList() {
      if (this.languageList.length === 0) {
        const { data } = await getLanguagesApi()
        this.setLanguageList(data)
      }
      return this.languageList
    },

    // 新增方法：获取语言项列表，如果 store 中没有数据则从 API 获取
    fetchLanguageItemList() {
      if (this.languageItemList.length === 0) {
        getLanguageItemsApi().then((res) => {
          this.setLanguageItemList(res.data)
        })
      }
      return this.languageItemList
    }
  }
})

export const useDictStoreWithOut = () => {
  return useDictStore(store)
}
