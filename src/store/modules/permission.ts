import { defineStore } from 'pinia'
import { constantRouterMap } from '@/router'
import { generateRoutesByServer, flatMultiLevelRoutes } from '@/utils/routerHelper'
import { store } from '../index'
import { cloneDeep } from 'lodash-es'
import { getMenuRouteApi } from '@/api/menu'
import router from '@/router'

export interface PermissionState {
  routers: AppRouteRecordRaw[]
  addRouters: AppRouteRecordRaw[]
  isAddRouters: boolean
  menuTabRouters: AppRouteRecordRaw[]
  permissionCodes: []
}

function collectPermissionCodes(menuItems) {
  const permissionCodes = []

  // 递归遍历菜单项
  function traverse(items) {
    for (const item of items) {
      // 如果存在 permission_code，则加入数组
      if (item.permission_code) {
        // @ts-ignore
        permissionCodes.push(item.permission_code)
      }

      // 如果存在 children，继续递归
      if (item.children && item.children.length > 0) {
        traverse(item.children)
      }
    }
  }

  // 开始遍历传入的菜单数组
  traverse(menuItems)

  return permissionCodes
}

export const usePermissionStore = defineStore('permission', {
  state: (): PermissionState => ({
    routers: [],
    addRouters: [],
    isAddRouters: false,
    menuTabRouters: [],
    permissionCodes: []
  }),
  getters: {
    getRouters(): AppRouteRecordRaw[] {
      return this.routers
    },
    getAddRouters(): AppRouteRecordRaw[] {
      return flatMultiLevelRoutes(cloneDeep(this.addRouters))
    },
    getIsAddRouters(): boolean {
      return this.isAddRouters
    },
    getMenuTabRouters(): AppRouteRecordRaw[] {
      return this.menuTabRouters
    },
    getPermissionCodes(): [] {
      return this.permissionCodes
    }
  },
  actions: {
    generateRoutes(routers?: AppCustomRouteRecordRaw[] | string[]): Promise<unknown> {
      return new Promise<void>((resolve) => {
        let routerMap: AppRouteRecordRaw[] = []
        routerMap = generateRoutesByServer(routers as AppCustomRouteRecordRaw[])
        // 动态路由，404一定要放到最后面
        this.addRouters = routerMap.concat([
          {
            path: '/:path(.*)*',
            redirect: '/404',
            name: '404Page',
            meta: {
              hidden: true,
              breadcrumb: false
            }
          }
        ])
        // 渲染菜单的所有路由
        this.routers = cloneDeep(constantRouterMap).concat(routerMap)
        resolve()
      })
    },
    setIsAddRouters(state: boolean): void {
      this.isAddRouters = state
    },
    setMenuTabRouters(routers: AppRouteRecordRaw[]): void {
      this.menuTabRouters = routers
    },
    setPermissionCodes(routers) {
      const permissionCodes = collectPermissionCodes(routers)
      this.permissionCodes = permissionCodes as []
    },
    async generateMenuRoute() {
      const res = await getMenuRouteApi()
      const roleRouters = res.data.list || []

      // 收集权限点
      this.setPermissionCodes(roleRouters)

      await this.generateRoutes(roleRouters as AppCustomRouteRecordRaw[])

      this.getAddRouters.forEach((route) => {
        router.addRoute(route as any) // 动态添加可访问路由表
      })

      this.setIsAddRouters(true)
    }
  }
  // persist: {
  //   paths: ['routers', 'addRouters', 'menuTabRouters']
  // }
})

export const usePermissionStoreWithOut = () => {
  return usePermissionStore(store)
}
