<template>
  <div class="mt-4 flex flex-col items-center" style="min-height: 100%">
    <ElForm>
      <div class="flex flex-col items-center justify-center">
        <div>
          <ElRadioGroup v-model="searchType">
            <ElRadio :value="1" size="large">精确搜索</ElRadio>
            <ElRadio :value="2" size="large">前缀搜索</ElRadio>
            <ElRadio :value="3" size="large">后缀搜索</ElRadio>
            <ElRadio :value="5" size="large">模糊搜索</ElRadio>
          </ElRadioGroup>
          <ElInput
            v-model="keyword"
            placeholder="请输入关键词进行搜索"
            class="input-with-select"
            @keydown.enter="handleEnter"
          >
            <template #append>
              <ElButton size="large" :loading="loading" @click="handleSearch">搜索一下</ElButton>
            </template>
          </ElInput>
        </div>
      </div>
    </ElForm>

    <div style="width: 70%" v-if="searched">
      <ElCard v-loading="loading" class="mt-4">
        <template #header v-if="tableData.length > 0">
          <div class="card-header">
            <!-- <ElButton type="primary" @click="exportExcel">导出</ElButton> -->
          </div>
        </template>
        <div>
          <ElTable :data="tableData" border style="width: 100%">
            <ElTableColumn prop="_source.filename" label="文件名" />
            <ElTableColumn label="操作" width="120">
              <template #default="{ row }">
                <!-- <ElButton :icon="Download" size="small" type="primary" @click="operate(row)"
                  >下载</ElButton
                > -->
                <ElButton
                  :icon="Position"
                  size="small"
                  type="primary"
                  @click="toDetail(row._source.cid)"
                  >CID详情</ElButton
                >
              </template>
            </ElTableColumn>
          </ElTable>

          <div class="flex items-center mt-2">
            <ElSelect v-model="size" style="width: 128px" @change="sizeChange">
              <ElOption :value="10" label="10条/页" />
              <ElOption :value="50" label="50条/页" />
              <ElOption :value="100" label="100条/页" />
            </ElSelect>
            <ElButton
              type="primary"
              class="ml-2"
              :disabled="currentPage === 1"
              :icon="ArrowLeft"
              @click="prevClick"
            />
            <ElButton
              type="primary"
              class="ml-2"
              :icon="ArrowRight"
              :disabled="tableData.length < size"
              @click="nextClick"
            />
          </div>
        </div>
        <!-- <ElEmpty v-else description="暂无数据" /> -->
      </ElCard>
    </div>
  </div>
</template>

<script setup>
import { getFileListAPI } from '@/api/file'
import { ArrowLeft, ArrowRight, Download, Position } from '@element-plus/icons-vue'
import { downloadRes } from '@/utils/download'

import {
  ElInput,
  ElSelect,
  ElOption,
  ElButton,
  ElForm,
  ElMessage,
  ElRow,
  ElCol,
  ElCard,
  ElTable,
  ElRadioGroup,
  ElRadio,
  ElTableColumn,
  ElPagination,
  ElEmpty
} from 'element-plus'
import { reactive, ref } from 'vue'
import { createSearchRecordApi } from '@/api/searchRecord'
import { onMounted } from 'vue'
import { useRoute } from 'vue-router'

const searchType = ref(1)
const keyword = ref('')
const loading = ref(false)
const searched = ref(false)

const currentPage = ref(1)
const size = ref(10)

const tableData = ref([])

const totalData = ref(0)

const handleEnter = (e) => {
  e.preventDefault()
}

const prevClick = () => {
  currentPage.value = currentPage.value - 1
  getFileList()
}
const nextClick = () => {
  currentPage.value = currentPage.value + 1
  getFileList()
}

const sizeChange = (val) => {
  currentPage.value = 1
  size.value = val
  getFileList()
}

const handleSizeChange = (number) => {
  size.value = number
  currentPage.value = 1

  getFileList()
}

const handleCurrentChange = (number) => {
  currentPage.value = number

  getFileList()
}

const operate = async (row) => {
  ElMessage.error('您的权限不足')
  //const params = {
  //  es_id: row._id,
  //  es_index: row._index
  //}
  //downloadRes(params)
}

const toDetail = (cid) => {
  const href = '/home/<USER>/result/' + cid
  window.open(href, '_blank')
}

const handleSearch = () => {
  if (!keyword.value) {
    ElMessage.error('请填写您需要搜索的关键字')
    return
  }
  currentPage.value = 1
  toSearch()
}

const toSearch = async () => {
  if (!keyword.value) {
    ElMessage.error('请填写您需要搜索的关键字')
    return
  }
  if (keyword.value.length < 2) {
    ElMessage.error('关键字最小长度为2个字符')
    return
  }
  const apiParams = {
    keyword: keyword.value,
    esType: searchType.value,
    // searchType: [10, 11],
    type: 3
  }
  // 记录日志
  createSearchRecordApi(apiParams)

  getFileList()
}

const getFileList = async () => {
  searched.value = true
  loading.value = true

  try {
    const { data } = await getFileListAPI({
      keyword: keyword.value,
      searchType: searchType.value,
      page: currentPage.value,
      limit: size.value
    })
    const { total, items } = data || {}
    //totalData.value = total
    tableData.value = items
  } finally {
    loading.value = false
  }
}

const { query } = useRoute()
onMounted(() => {
  let { keyword: kv, esType } = query || {}
  if (kv && esType) {
    keyword.value = kv
    searchType.value = esType ? +esType : 1
    toSearch()
  }
})

const exportExcel = async () => {
  if (!keyword.value) {
    ElMessage.error('请填写您需要搜索的关键字')
    return
  }

  loading.value = true

  try {
    const response = await exportPasswordListAPI({
      keyword: keyword.value,
      searchType: searchType.value
    })

    // 创建一个 Blob 对象
    const blob = new Blob([response.data], { type: 'text/csv' })

    // 从响应头中提取文件名
    const contentDisposition = response.headers['content-disposition']
    const filename = contentDisposition.split('filename=')[1].replace(/"/g, '')

    // 创建一个下载链接
    const link = document.createElement('a')
    link.href = window.URL.createObjectURL(blob)
    link.download = filename

    // 模拟点击下载链接
    document.body.appendChild(link)
    link.click()

    // 移除下载链接
    document.body.removeChild(link)
  } finally {
    loading.value = false
  }
}
</script>

<style lang="scss" scoped></style>
