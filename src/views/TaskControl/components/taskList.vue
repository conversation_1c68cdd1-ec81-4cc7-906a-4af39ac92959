<template>
  <div>
    <el-dialog
      v-model="dialogVisible"
      title="任务列表"
      :fullscreen="true"
      :before-close="handleClose"
    >
      <div class="flex items-center justify-between mb-4">
        <div>
          <span>是否只显示已命中记录</span>
          <ElSwitch @click="getList" v-model="show_hit" />
        </div>
        <div>
          <ElButton type="primary" plain @click="download" :loading="downloadLoading"
            >导出</ElButton
          >
        </div>
      </div>
      <Table
        v-model:current-page="currentPage"
        v-model:page-size="pageSize"
        :columns="allSchemas.tableColumns"
        :data="dataList"
        :loading="loading"
        @register="tableRegister"
        :pagination="{
          total
        }"
      />
      <DetailModule ref="detailRef" />
    </el-dialog>
  </div>
</template>

<script setup lang="tsx">
// import { getTaskListApi } from '@/api/task'
import { Table } from '@/components/Table'
import { useTable } from '@/hooks/web/useTable'
import { CrudSchema, useCrudSchemas } from '@/hooks/web/useCrudSchemas'
import { reactive, ref, unref } from 'vue'
import { filterType, filterStatus } from '@/utils/filter'
import { ElTag, ElDialog, ElSwitch, ElButton, ElMessage } from 'element-plus'
import dayjs from 'dayjs'
import { BaseButton } from '@/components/Button'
import DetailModule from './detail.vue'
import { createSearchRecordApi } from '@/api/searchRecord'
import { useRouter } from 'vue-router'
import { getTaskControlTasksApi, downloadTaskControlApi } from '@/api/taskControl'

const dialogVisible = ref(false)
const show_hit = ref(false)

const handleClose = () => {
  dialogVisible.value = false
}

const taskControlId = ref()

const { tableRegister, tableState, tableMethods } = useTable({
  fetchDataApi: async () => {
    let items = []
    let total = 0

    if (taskControlId.value) {
      const { pageSize, currentPage } = tableState
      const res = await getTaskControlTasksApi({
        page: unref(currentPage),
        size: unref(pageSize),
        task_control_id: taskControlId.value,
        show_hit: show_hit.value ? 1 : 0
      })
      items = res.data.items || []
      total = res.data.total
    }

    return {
      list: items,
      total: total
    }
  }
})

const { total, loading, dataList, pageSize, currentPage } = tableState
const { getList } = tableMethods

const crudSchemas = reactive<CrudSchema[]>([
  {
    field: 'selection',
    search: {
      hidden: true
    },
    form: {
      hidden: true
    },
    detail: {
      hidden: true
    },
    table: {
      type: 'selection'
    }
  },
  {
    field: 'index',
    label: '序号',
    type: 'index',
    search: {
      hidden: true
    }
  },
  {
    field: 'keyword',
    label: '搜索关键词',
    minWidth: '120'
  },
  {
    field: 'es_type',
    label: '搜索类型',
    minWidth: '100',
    search: {
      hidden: true
    },
    slots: {
      default: (data: any) => {
        return (
          <>
            <ElTag type={filterType(data.row.task_control.es_type, true)}>
              {filterType(data.row.task_control.es_type)}
            </ElTag>
          </>
        )
      }
    }
  },
  {
    field: 'search_field',
    label: '搜索范围',
    minWidth: '100',
    search: {
      hidden: true
    },
    slots: {
      default: ({ row }) => {
        return <ElTag type="primary">{row?.task_control?.search_field?.name}</ElTag>
      }
    }
  },
  {
    field: 'countries',
    label: '国家/地区',
    minWidth: '100',
    search: {
      hidden: true
    },
    slots: {
      default: ({ row }) => {
        return row?.task_control?.countries?.map((i) => <ElTag key={i.id}>{i.name}</ElTag>)
      }
    }
  },
  {
    field: 'languages',
    label: '语言',
    minWidth: '100',
    search: {
      hidden: true
    },
    slots: {
      default: ({ row }) => {
        return row?.task_control?.languages?.map((i) => <ElTag key={i.id}>{i.name}</ElTag>)
      }
    }
  },
  // {
  //   field: 'is_cached',
  //   label: '是否已缓存',
  //   minWidth: '100',
  //   search: {
  //     hidden: true
  //   },
  //   slots: {
  //     default: (data: any) => {
  //       return (
  //         <>
  //           <ElTag type={data.row?.task ? 'success' : 'danger'}>
  //             {data.row?.task ? '是' : '否'}
  //           </ElTag>
  //         </>
  //       )
  //     }
  //   }
  // },
  {
    field: 'status',
    label: '状态',
    search: {
      hidden: true
    },
    minWidth: '100',
    slots: {
      default: (data: any) => {
        return data.row?.task?.status ? (
          <ElTag type={filterStatus(data.row.task.status, true)}>
            {filterStatus(data.row.task.status)}
          </ElTag>
        ) : null
      }
    }
  },
  // {
  //   field: 'task.start_at',
  //   label: '开始时间',
  //   minWidth: '160',
  //   search: {
  //     hidden: true
  //   }
  // },
  // {
  //   field: 'task.finished_at',
  //   label: '完成时间',
  //   minWidth: '160',
  //   search: {
  //     hidden: true
  //   }
  // },
  // {
  //   field: 'task.expired_at',
  //   label: '过期时间',
  //   minWidth: '160',
  //   search: {
  //     hidden: true
  //   }
  // },
  // {
  //   field: 'task.is_expired',
  //   label: '是否已过期',
  //   minWidth: '100',
  //   search: {
  //     hidden: true
  //   },
  //   slots: {
  //     default: (data: any) => {
  //       return (
  //         <>
  //           <ElTag type={dayjs(data.row?.task?.expired_at) < dayjs() ? 'success' : 'danger'}>
  //             {dayjs(data.row?.task?.expired_at) < dayjs() ? '是' : '否'}
  //           </ElTag>
  //         </>
  //       )
  //     }
  //   }
  // },
  {
    field: 'task.hit_total',
    label: '命中数量',
    minWidth: '100',
    search: {
      hidden: true
    }
  },
  {
    field: 'task.user.username',
    label: '操作人',
    minWidth: '120',
    search: {
      hidden: true
    }
  },

  {
    field: 'action',
    label: '操作',
    search: {
      hidden: true
    },
    fixed: 'right',
    minWidth: '250',
    slots: {
      default: (data: any) => {
        const row = data.row
        return (
          <>
            {/* <BaseButton type="primary" size="small" onClick={() => detail(row)}>
              详情
            </BaseButton> */}

            <BaseButton type="success" size="small" onClick={() => searchTo(row)}>
              查看结果
            </BaseButton>
          </>
        )
      }
    }
  }
])

const { allSchemas } = useCrudSchemas(crudSchemas)

const detailRef = ref()
const detail = (row) => {
  detailRef.value.open(row.task.id)
}

const router = useRouter()

const searchTo = (row) => {
  const apiParams = {
    keyword: row.keyword,
    searchField: row.task_control.search_field.key,
    searchCountries: row.task_control.countries.map((i) => i.id),
    searchLanguages: row.task_control.languages.map((i) => i.id),
    esType: row.task_control.es_type,
    type: 1
  }
  // 记录日志
  createSearchRecordApi(apiParams)
  const resultTo = router.resolve({
    name: 'Result',
    query: apiParams
  })
  window.open(resultTo.href, '_blank')
}

const downloadLoading = ref(false)

const download = async (row) => {
  // 设置当前行的下载按钮为 loading 状态
  downloadLoading.value = true
  try {
    const response = (await downloadTaskControlApi({
      task_control_id: taskControlId.value,
      show_hit: show_hit.value ? 1 : 0
    })) as any
    // 创建一个 Blob 对象
    const blob = new Blob([response.data], { type: 'text/csv' })

    // 从响应头中提取文件名
    const contentDisposition = response.headers['content-disposition']
    const filename = contentDisposition.split('filename=')[1].replace(/"/g, '')

    // 创建一个下载链接
    const link = document.createElement('a')
    link.href = window.URL.createObjectURL(blob)
    link.download = decodeURI(filename)

    // 模拟点击下载链接
    document.body.appendChild(link)
    link.click()

    // 移除下载链接
    document.body.removeChild(link)
  } catch (e: any) {
    const jsonData = await e.response.data.text()
    const parsedData = JSON.parse(jsonData) // 解析 JSON
    const { msg } = parsedData || {}
    ElMessage.error(msg)
  } finally {
    // 无论成功还是失败，都取消 loading 状态
    downloadLoading.value = false
  }
}

const open = (id) => {
  taskControlId.value = id
  getList()
  dialogVisible.value = true
}

defineExpose({
  open
})
</script>

<style lang="scss" scoped></style>
