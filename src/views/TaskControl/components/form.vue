<template>
  <div>
    <el-dialog v-model="dialogVisible" title="修改" :before-close="handleClose">
      <ElForm :model="searchForm" :rules="rules" label-position="top" ref="formRef">
        <ElFormItem label="循环周期" prop="cycle_days">
          <ElInputNumber v-model="searchForm.cycle_days" />
        </ElFormItem>
        <ElFormItem label="备注" prop="notes">
          <ElInput v-model="searchForm.notes" clearable placeholder="请输入备注" />
        </ElFormItem>
      </ElForm>
      <template #footer>
        <BaseButton type="primary" :loading="saveLoading" @click="save"> 保存 </BaseButton>
        <BaseButton @click="dialogVisible = false">关闭</BaseButton>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
import { reactive, ref } from 'vue'
import { ElDialog, ElForm, ElFormItem, ElInput, ElMessage, ElInputNumber } from 'element-plus'
import { updateTaskControlApi } from '@/api/taskControl'

const dialogVisible = ref(false)
const saveLoading = ref(false)

const searchForm = ref()
const formRef = ref()

const emits = defineEmits(['success'])

const rules = reactive({
  notes: [{ required: true, message: '请输入备注' }],
  cycle_days: [{ required: true, message: '请输入循环周期' }]
})

const handleClose = () => {
  dialogVisible.value = false
}

const save = async () => {
  formRef.value.validate(async (valid) => {
    if (valid) {
      saveLoading.value = true
      await updateTaskControlApi(searchForm.value.id, {
        notes: searchForm.value.notes,
        cycle_days: searchForm.value.cycle_days
      })

      saveLoading.value = false
      dialogVisible.value = false
      ElMessage.success('操作成功')
      emits('success')
    }
  })
}

const open = (form) => {
  searchForm.value = Object.assign({}, form)
  dialogVisible.value = true
}

defineExpose({
  open
})
</script>

<style lang="scss" scoped></style>
