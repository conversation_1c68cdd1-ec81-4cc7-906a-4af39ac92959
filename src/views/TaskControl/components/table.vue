<template>
  <div>
    <Table
      v-model:current-page="currentPage"
      v-model:page-size="pageSize"
      :columns="allSchemas.tableColumns"
      :data="dataList"
      :loading="loading"
      @register="tableRegister"
      :pagination="{
        total
      }"
    />
    <TaskListDialog ref="taskListRef" />
    <FormModule ref="formModuleRef" @success="getList" />
  </div>
</template>

<script setup lang="tsx">
import {
  getTaskControlListApi,
  updateTaskControlApi,
  deleteTaskControlApi
} from '@/api/taskControl'
import { Table } from '@/components/Table'
import { useTable } from '@/hooks/web/useTable'
import { CrudSchema, useCrudSchemas } from '@/hooks/web/useCrudSchemas'
import { reactive, ref, unref } from 'vue'
import { filterStatus, filterExecType } from '@/utils/filter'
import { ElMessage, ElSwitch, ElTag, ElMessageBox } from 'element-plus'
import { BaseButton } from '@/components/Button'
import TaskListDialog from './taskList.vue'
import FormModule from './form.vue'

const searchParams = ref({})

const taskListRef = ref()
const formModuleRef = ref()

const { tableRegister, tableState, tableMethods } = useTable({
  fetchDataApi: async () => {
    let items = []
    let total = 0
    const { pageSize, currentPage } = tableState
    const res = await getTaskControlListApi({
      page: unref(currentPage),
      size: unref(pageSize),
      ...unref(searchParams)
    })
    const { data } = res
    items = data.items
    total = data.total
    return {
      list: items,
      total: total
    }
  }
})

const { total, loading, dataList, pageSize, currentPage } = tableState
const { getList } = tableMethods

const crudSchemas = reactive<CrudSchema[]>([
  {
    field: 'selection',
    search: {
      hidden: true
    },
    form: {
      hidden: true
    },
    detail: {
      hidden: true
    },
    table: {
      type: 'selection'
    }
  },
  {
    field: 'index',
    label: '序号',
    type: 'index',
    search: {
      hidden: true
    }
  },
  {
    field: 'notes',
    label: '备注'
  },
  {
    field: 'keyword_num',
    minWidth: '100',
    label: '关键词数'
  },
  {
    field: 'exec_type',
    minWidth: '100',
    label: '执行类型',
    slots: {
      default: (data: any) => {
        return (
          <>
            <ElTag type={filterExecType(data.row.exec_type, true)}>
              {filterExecType(data.row.exec_type)}
            </ElTag>
          </>
        )
      }
    }
  },
  // {
  //   field: 'es_type',
  //   label: '搜索类型',
  //   minWidth: '120',
  //   search: {
  //     hidden: true
  //   },
  //   slots: {
  //     default: (data: any) => {
  //       return (
  //         <>
  //           <ElTag type={filterType(data.row.es_type, true)}>{filterType(data.row.es_type)}</ElTag>
  //         </>
  //       )
  //     }
  //   }
  // },
  // {
  //   field: 'search_field',
  //   label: '搜索范围',
  //   minWidth: '120',
  //   search: {
  //     hidden: true
  //   },
  //   slots: {
  //     default: ({ row }) => {
  //       return <ElTag type="primary">{row.search_field.name}</ElTag>
  //     }
  //   }
  // },
  // {
  //   field: 'countries',
  //   label: '国家/地区',
  //   minWidth: '100',
  //   search: {
  //     hidden: true
  //   },
  //   slots: {
  //     default: ({ row }) => {
  //       return row.countries.map((i) => <ElTag key={i.id}>{i.name}</ElTag>)
  //     }
  //   }
  // },
  // {
  //   field: 'languages',
  //   label: '语言',
  //   minWidth: '100',
  //   search: {
  //     hidden: true
  //   },
  //   slots: {
  //     default: ({ row }) => {
  //       return row.languages.map((i) => <ElTag key={i.id}>{i.name}</ElTag>)
  //     }
  //   }
  // },
  {
    field: 'status',
    label: '状态',
    minWidth: '100',
    search: {
      hidden: true
    },
    slots: {
      default: (data: any) => {
        return (
          <>
            <ElTag type={filterStatus(data.row.status, true)}>
              {filterStatus(data.row.status)}
            </ElTag>
          </>
        )
      }
    }
  },
  {
    field: 'cycle_days',
    label: '循环周期',
    minWidth: '100',
    search: {
      hidden: true
    }
  },
  {
    field: 'start_at',
    label: '开始时间',
    minWidth: '160',
    search: {
      hidden: true
    }
  },
  {
    field: 'next_start_at',
    label: '下次执行时间',
    minWidth: '160',
    search: {
      hidden: true
    }
  },
  {
    field: 'enabled',
    label: '启用',
    search: {
      hidden: true
    },
    slots: {
      default: ({ row }) => {
        return (
          <ElSwitch
            modelValue={Boolean(row.enabled)}
            onChange={async (val) => {
              try {
                await updateTaskControlApi(row.id, {
                  enabled: val ? 1 : 0
                })
                row.enabled = val ? 1 : 0
                ElMessage.success('更新成功')
              } catch (error) {
                ElMessage.error('更新失败')
              }
            }}
          />
        )
      }
    }
  },
  {
    field: 'user.username',
    label: '操作人',
    search: {
      hidden: true
    }
  },

  {
    field: 'action',
    label: '操作',
    search: {
      hidden: true
    },
    fixed: 'right',
    minWidth: '230',
    slots: {
      default: (data: any) => {
        const row = data.row
        return (
          <>
            <BaseButton type="primary" size="small" onClick={() => detail(row)}>
              详情
            </BaseButton>
            <BaseButton type="success" size="small" onClick={() => edit(row)}>
              编辑
            </BaseButton>
            <BaseButton type="danger" size="small" onClick={() => delData(row)}>
              删除
            </BaseButton>
          </>
        )
      }
    }
  }
])

const { allSchemas } = useCrudSchemas(crudSchemas)

const detail = (row) => {
  taskListRef.value.open(row.id)
}

const edit = (row) => {
  formModuleRef.value.open(row)
}

const delData = async (row: any = null) => {
  ElMessageBox.confirm('是否删除此条记录', '提示', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning'
  })
    .then(async () => {
      const id = row.id
      await deleteTaskControlApi(id).finally(() => {})

      getList()
    })
    .catch(() => {})
}

defineExpose({
  getList
})
</script>

<style lang="scss" scoped></style>
