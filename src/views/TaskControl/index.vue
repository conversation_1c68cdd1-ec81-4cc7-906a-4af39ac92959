<template>
  <div class="search-page">
    <ElRow :gutter="10">
      <ElCol :span="5">
        <el-scrollbar :height="'calc(100vh - 70px)'">
          <el-form
            :validate-on-rule-change="false"
            :model="searchForm"
            label-position="top"
            ref="formRef"
            :rules="rules"
          >
            <div class="search-group">
              <div class="search-row">
                <el-form-item label="搜索类型" prop="esType">
                  <el-radio-group v-model="searchForm.esType">
                    <el-radio-button label="精确" :value="1">精确搜索</el-radio-button>
                    <el-radio-button label="模糊" :value="5">模糊搜索</el-radio-button>
                  </el-radio-group>
                </el-form-item>
                <el-form-item label="执行类型" prop="execType">
                  <el-radio-group v-model="searchForm.execType">
                    <el-radio-button label="延迟执行" :value="1">延迟执行</el-radio-button>
                    <el-radio-button label="立即执行" :value="2">立即执行</el-radio-button>
                  </el-radio-group>
                </el-form-item>
                <el-form-item label="循环周期" prop="cycleDays">
                  <el-input-number v-model="searchForm.cycleDays" :min="1" />
                </el-form-item>
                <el-form-item label="搜索范围" prop="searchField">
                  <el-select v-model="searchForm.searchField" placeholder="选择范围">
                    <ElOption
                      v-for="(item, key) in fields"
                      :key="key"
                      :value="item.key"
                      :label="item.name"
                    />
                  </el-select>
                </el-form-item>
                <el-form-item label="选择搜索国家/地区" prop="searchCountries">
                  <FilterSelect
                    v-model="searchForm.searchCountries"
                    :options="countriesList"
                    placeholder="选择搜索国家/地区"
                    :multiple="true"
                  >
                    <template #default="{ item }">
                      <div class="flex justify-between">
                        <span>{{ item.name }}</span>
                        <span>{{ item.code }}</span>
                      </div>
                    </template>
                  </FilterSelect>
                </el-form-item>
                <el-form-item label="搜索语言" prop="searchLanguages">
                  <FilterSelect
                    v-model="searchForm.searchLanguages"
                    :options="languagesList"
                    placeholder="选择搜索语言"
                    :multiple="true"
                  />
                </el-form-item>
                <el-form-item label="关键字" class="keyword-input" prop="keyword">
                  <el-input
                    type="textarea"
                    v-model="searchForm.keyword"
                    :autosize="{ minRows: 4 }"
                    placeholder="请输入关键字，每个关键词占一行"
                    @keyup.enter="validateKeywordLine"
                  />
                </el-form-item>
                <el-form-item label="任务备注" class="keyword-input" prop="notes">
                  <el-input
                    type="textarea"
                    v-model="searchForm.notes"
                    :autosize="{ minRows: 4 }"
                    placeholder="请输入任务备注"
                  />
                </el-form-item>
              </div>
            </div>
          </el-form>
          <div class="search-actions">
            <div class="flex justify-between">
              <div
                >当前任务池中的任务数：<strong>{{ currentPoolNumber }}</strong></div
              >
              <div
                >任务池最大任务数：<strong>{{ currentPoolMaxNumber }}</strong></div
              >
            </div>
            <div class="flex justify-between">
              <el-button
                class="mt-4"
                type="success"
                :loading="loading"
                @click="handleSearch"
                :icon="Promotion"
              >
                开始布控
              </el-button>

              <el-button
                class="mt-4"
                type="warning"
                :loading="loading"
                @click="handleResetPool"
                :icon="Refresh"
              >
                重置任务池
              </el-button>
            </div>
          </div>
        </el-scrollbar>
      </ElCol>
      <ElDivider direction="vertical" style="height: auto" />
      <ElCol :span="18">
        <el-card class="search-card" header="布控记录">
          <template #header>
            <div style="display: flex; justify-content: space-between; align-items: center">
              <span>布控记录</span>
              <el-button type="primary" plain @click="refreshTable">刷新</el-button>
            </div>
          </template>
          <TableList ref="tableListRef" />
        </el-card>
      </ElCol>
    </ElRow>
  </div>
</template>

<script setup>
import { reactive, computed, ref, nextTick } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { Search, Delete, Plus, Refresh, Promotion } from '@element-plus/icons-vue'
import {
  ElCard,
  ElButton,
  ElForm,
  ElFormItem,
  ElRadioGroup,
  ElRadioButton,
  ElSelect,
  ElOption,
  ElInput,
  ElInputNumber,
  ElIcon,
  ElCollapse,
  ElCollapseItem,
  ElRow,
  ElCol,
  ElDivider,
  ElScrollbar
} from 'element-plus'
import { searchTypes } from '@/constants/searchType'
import { getCountryList, getLanguageList } from '@/utils/cache'
import { onMounted } from 'vue'
import FilterSelect from '@/components/FilterSelect/index.vue'
import TableList from './components/table.vue'
import { getTaskPoolApi } from '@/api/task'
import { createTaskControlApi, resetPoolApi } from '@/api/taskControl'
import { getFieldsApi } from '@/api/home'
import { getSettingByCodeApi } from '@/api/setting'
import { getPushConnection } from '@/utils/push'
import { cloneDeep } from 'lodash-es'
import { useUserStore } from '@/store/modules/user'

const activeNames = ref('1')

const userStore = useUserStore()

const userId = computed(() => userStore.getUserInfo?.id)

// 定义默认值对象
const defaultForm = {
  esType: 1,
  searchField: null,
  cycleDays: null,
  searchCountries: [],
  searchLanguages: [],
  keyword: null,
  execType: 1,
  notes: null
}

const searchForm = reactive({ ...defaultForm })

const rules = reactive({
  cycleDays: [{ required: true, message: '请填写循环周期', trigger: 'change' }], // 添加触发方式
  searchField: [{ required: true, message: '请选择搜索类型', trigger: 'change' }],
  keyword: [
    { required: true, message: '请填写搜索关键字', trigger: 'change' },
    {
      validator: (rule, value, callback) => {
        if (!value) {
          callback()
          return
        }
        const keywords = value.split('\n').filter((k) => k.trim())
        const invalidKeywords = keywords.filter((k) => k.trim().length < 2)
        if (invalidKeywords.length > 0) {
          callback(new Error('每个关键词最小长度为2个字符'))
        } else {
          callback()
        }
      },
      trigger: 'change'
    }
  ],
  notes: [{ required: true, message: '请填写任务备注', trigger: 'change' }]
})

const validateKeywordLine = (e) => {
  const text = searchForm.keyword

  // 如果最后一个字符是换行符,说明是最后的空行,不需要验证
  if (text.endsWith('\n')) {
    return
  }

  const lines = text.split('\n')
  const currentLine = lines[lines.length - 1]

  if (currentLine.trim().length < 2) {
    ElMessage.warning('每个关键词最小长度为2个字符')
    // 移除最后一个换行
    searchForm.keyword = lines.slice(0, -1).join('\n')
    return
  }
}

const loading = ref(false)
const formRef = ref()

const originCountriesList = ref([])
const originLanguagesList = ref([])

const countriesList = ref([])
const languagesList = ref([])
const fields = ref([])
const tableListRef = ref()

const countryInput = ref(null)
const countryFilter = (val) => {
  if (!val) {
    countriesList.value = originCountriesList.value
  } else {
    countriesList.value = countriesList.value.filter((item) => {
      return item.name.indexOf(val) > -1 || item.code.toUpperCase().indexOf(val.toUpperCase()) > -1
    })
  }
}

const languageInput = ref(null)
const languageFilter = (val) => {
  if (!val) {
    languagesList.value = originLanguagesList.value
  } else {
    languagesList.value = languagesList.value.filter((item) => {
      return item.name.indexOf(val) > -1
    })
  }
}

const getCountriesData = async () => {
  const data = await getCountryList()
  countriesList.value = data
  originCountriesList.value = data
}

const getLanguagesData = async () => {
  const data = await getLanguageList()
  languagesList.value = data
  originLanguagesList.value = data
}

const getFields = async () => {
  const { data } = await getFieldsApi()
  fields.value = data
}

const removeSearchGroup = (index) => {
  if (searchForm.groups.length > 1) {
    searchForm.groups.splice(index, 1)
  }
}

const handleSearch = async () => {
  try {
    // 手动触发验证
    const valid = await formRef.value.validate()
    if (!valid) return

    loading.value = true
    await createTaskControlApi({
      ...searchForm
    })

    resetSearch()
    refreshTable()
    ElMessage.success('任务提交成功')
  } catch (error) {
    console.error(error)
    ElMessage.error('提交任务失败')
  } finally {
    loading.value = false
  }
}

const handleResetPool = async () => {
  ElMessageBox.confirm('是否重置任务池', '提示', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning'
  }).then(async () => {
    await resetPoolApi()
    getTaskPoolData()
    ElMessage.success('任务池重置成功')
  })
}

const resetSearch = async () => {
  Object.keys(searchForm).forEach((key) => {
    searchForm[key] = defaultForm[key]
  })
  // await nextTick()
  formRef.value.resetFields()
  await getHitCacheDaySetting()
}

const currentPoolNumber = ref(0)
const currentPoolMaxNumber = ref(0)
const getTaskPoolData = async () => {
  const { data } = await getTaskPoolApi()
  currentPoolNumber.value = data.taskTotal || 0
  currentPoolMaxNumber.value = data.taskPoolMax || 0
}

const listenMessage = () => {
  const connection = getPushConnection()
  // 创建监听频道
  const channel = connection.subscribe('task_pool_push')
  channel.on('change_pool_number', function (data) {
    currentPoolNumber.value = data.number
  })
  channel.on('task_control_status_update:userId:' + userId.value, function (data) {
    refreshTable()
  })
}

const refreshTable = () => {
  tableListRef.value.getList()
}

const getHitCacheDaySetting = async () => {
  const { data } = await getSettingByCodeApi({
    code: 'hit_cache_days'
  })
  const { val } = data || {}
  searchForm.cycleDays = val ? +val : 7
}

onMounted(async () => {
  await Promise.all([
    getCountriesData(),
    getLanguagesData(),
    getFields(),
    getTaskPoolData(),
    getHitCacheDaySetting()
  ])

  listenMessage()
})
</script>
