<script setup lang="ts">
import { LoginForm, RegisterForm } from './components'
import { ThemeSwitch } from '@/components/ThemeSwitch'
import { LocaleDropdown } from '@/components/LocaleDropdown'
import { useI18n } from '@/hooks/web/useI18n'
import { getCssVar, underlineToHump } from '@/utils'
import { useAppStore } from '@/store/modules/app'
import { useDesign } from '@/hooks/web/useDesign'
import { ref } from 'vue'
import { ElScrollbar } from 'element-plus'

const { getPrefixCls } = useDesign()

const prefixCls = getPrefixCls('login')

const appStore = useAppStore()

const title = ref('深海智能体')

const { t } = useI18n()

const isLogin = ref(true)

const toRegister = () => {
  isLogin.value = false
}

const toLogin = () => {
  isLogin.value = true
}

const themeChange = () => {
  const color = getCssVar('--el-bg-color')
  appStore.setMenuTheme(color)
  appStore.setHeaderTheme(color)
}
</script>

<template>
  <div
    :class="prefixCls"
    class="login-bg w-full h-screen flex items-center justify-center custom-bg"
  >
    <div class="relative z-10 w-full max-w-600px">
      <div class="text-center mb-40px">
        <div class="text-3xl font-bold text-white mb-8px">深海智能体</div>
        <div class="text-lg text-blue-200 mb-30px">欢迎登录</div>
      </div>
      <div class="login-panel p-50px shadow-2xl">
        <LoginForm class="m-0 p-0" @to-register="toRegister" />
        <!-- <div class="flex justify-between items-center mt-30px">
          <span class="text-gray-300 text-base">忘记密码?</span>
          <a class="text-blue-500 text-base hover:underline cursor-pointer">立即找回</a>
        </div> -->
      </div>
    </div>
  </div>
</template>

<style lang="less" scoped>
@prefix-cls: ~'@{namespace}-login';

.login-bg {
  min-height: 100vh;
  min-width: 100vw;
  position: relative;
  overflow: hidden;
}

.custom-bg {
  background-image: url('@/assets/imgs/login-bg.jpg');
  background-size: cover;
  background-position: center;
}

.bg-opacity-80 {
  background: rgba(0, 0, 0, 0.8);
}

.login-panel {
  background: none;
  border-radius: 24px;
  // box-shadow: 0 12px 48px 0 rgba(31, 38, 135, 0.18);
  padding: 50px 48px 40px 48px;
  min-width: 400px;
}

.shadow-2xl {
  // box-shadow: 0 12px 48px 0 rgba(31, 38, 135, 0.18);
}

.rounded-2xl {
  border-radius: 24px;
}
</style>
