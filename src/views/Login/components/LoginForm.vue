<script setup>
import { ref, watch, onMounted, reactive, h } from 'vue'
import { getUserinfoApi, loginApi, getCaptcha } from '@/api/auth'
import { useRouter } from 'vue-router'
import { useUserStore } from '@/store/modules/user'
import { User, Lock, Message } from '@element-plus/icons-vue'
import { ElForm, ElFormItem, ElInput, ElButton, ElIcon, ElNotification } from 'element-plus'

const emit = defineEmits(['to-register'])

const userStore = useUserStore()

const { currentRoute, push } = useRouter()

import { encryptString } from '@/utils/crypto'

const form = ref({
  username: null,
  password: null,
  code: null,
  uuid: null
})

const captcha = ref(null)

const loading = ref(false)

const redirect = ref()

const refreshCaptcha = async () => {
  form.value.code = null
  form.value.uuid = null
  const captchaRes = await getCaptcha()

  captcha.value = 'data:image/jpeg;base64,' + captchaRes.data.img
  form.value.uuid = captchaRes.data.uuid
}

refreshCaptcha()

watch(
  () => currentRoute.value,
  (route) => {
    redirect.value = route?.query?.redirect
  },
  {
    immediate: true
  }
)

const formRef = ref()

const rules = reactive({
  username: [
    {
      required: true,
      message: '请填写用户名'
    }
  ],
  password: [
    {
      required: true,
      message: '请填写密码'
    }
  ],
  code: [
    {
      required: true,
      message: '请填写验证码'
    }
  ]
})

// 登录
const handleLogin = async () => {
  await formRef.value.validate(async (valid) => {
    if (valid) {
      loading.value = true
      const formData = form.value

      try {
        const formatData = Object.assign({}, formData)
        formatData.password = encryptString(formatData.password)

        await userStore.requestLogin(formatData)
        ElNotification({
          title: '系统重大更新提示',
          message: h('div', [
            h('div', { style: 'line-height: 1.6;' }, [
              h('div', '1. 搜索优化'),
              h('div', '2. 数据大批量更新'),
              h('div', '3. 系统新功能即将开放，敬请期待')
            ])
          ]),
          type: 'success',
          duration: 10000
        })
        push({ path: redirect.value || '/' })
      } catch (error) {
        refreshCaptcha()
      } finally {
        loading.value = false
      }
    }
  })
}
</script>

<template>
  <div style="width: 100%" class="text-dark">
    <ElForm :model="form" :rules="rules" ref="formRef" class="login-form-custom">
      <ElFormItem :hide-label="true" prop="username">
        <ElInput
          v-model="form.username"
          class="w-full login-input"
          size="large"
          placeholder="请填写用户名"
          allow-clear
        >
          <template #prefix>
            <ElIcon><User /></ElIcon>
          </template>
        </ElInput>
      </ElFormItem>

      <ElFormItem :hide-label="true" prop="password">
        <ElInput
          type="password"
          v-model="form.password"
          class="login-input"
          placeholder="请输入您的密码"
          size="large"
          allow-clear
        >
          <template #prefix>
            <ElIcon><Lock /></ElIcon>
          </template>
        </ElInput>
      </ElFormItem>

      <ElFormItem :hide-label="true" prop="code">
        <div class="flex items-center w-full">
          <ElInput
            v-model="form.code"
            placeholder="请输入验证码"
            size="large"
            allow-clear
            class="login-input flex-1"
          >
            <template #prefix>
              <ElIcon><Message /></ElIcon>
            </template>
          </ElInput>
          <img :src="captcha" class="captcha-img ml-2" @click="refreshCaptcha" />
        </div>
      </ElFormItem>

      <ElFormItem class="mt-5">
        <ElButton
          type="primary"
          class="login-btn"
          size="large"
          :loading="loading"
          @click="handleLogin"
        >
          登录
        </ElButton>
      </ElFormItem>
    </ElForm>
  </div>
</template>

<style scoped>
.login-form-custom {
  padding: 0;
}
.login-input {
  background: transparent !important;
  border-radius: 8px;
  box-shadow: 0 2px 8px 0 rgba(31, 38, 135, 0.08);
  border: 2.5px solid #3a4a5a !important;
  transition: border-color 0.2s;
}
.login-input:focus,
:deep(.el-input.is-focus .el-input__inner) {
  border-color: #1890ff !important;
  box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.1);
}
.captcha-img {
  height: 36px;
  width: 90px;
  border-radius: 6px;
  cursor: pointer;
  box-shadow: 0 2px 8px 0 rgba(31, 38, 135, 0.08);
  background: #fff;
  object-fit: cover;
}
.login-btn {
  width: 100%;
  border-radius: 8px;
  background: linear-gradient(90deg, #1890ff 0%, #1e90ff 100%);
  color: #fff;
  font-weight: bold;
  box-shadow: 0 4px 16px 0 rgba(24, 144, 255, 0.15);
  letter-spacing: 2px;
}
:deep(.el-input__inner) {
  color: #fff !important;
  background: transparent !important;
  caret-color: #fff !important;
  border: none !important;
}
:deep(.el-input__inner::placeholder) {
  color: #cccccc !important;
  opacity: 1;
}
:deep(.el-input.is-focus .el-input__inner) {
  box-shadow: none !important;
}
:deep(.el-input__wrapper) {
  background: transparent !important;
  box-shadow: none !important;
}
:deep(.el-input__wrapper) {
  border: none !important;
}
</style>
