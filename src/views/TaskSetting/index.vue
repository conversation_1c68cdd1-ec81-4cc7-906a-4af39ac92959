<template>
  <div class="container mx-auto">
    <div class="flex justify-center">
      <ElCard header="任务设置" class="w-full">
        <ElDescriptions border :column="1">
          <ElDescriptionsItem label="任务命中结果最大数">
            <ElInputNumber v-model="form.hit_max_number" :min="1" />
          </ElDescriptionsItem>
          <ElDescriptionsItem label="命中结果缓存天数">
            <ElInputNumber v-model="form.hit_cache_days" :min="1" />
          </ElDescriptionsItem>
          <ElDescriptionsItem label="任务池最大数">
            <ElInputNumber v-model="form.task_pool_max" :min="1" />
          </ElDescriptionsItem>
        </ElDescriptions>
        <template #footer>
          <ElButton type="primary" :loading="loading" @click="handleSave"> 保存 </ElButton>
        </template>
      </ElCard>
    </div>
  </div>
</template>

<script setup>
import { getSettingList<PERSON>pi, putSettingApi } from '@/api/setting'
import {
  ElCard,
  ElDescriptions,
  ElDescriptionsItem,
  ElButton,
  ElInputNumber,
  ElMessage
} from 'element-plus'
import { onMounted, reactive, ref } from 'vue'

const form = ref({
  hit_max_number: 0, // 初始值
  hit_cache_days: 0,
  task_pool_max: 0 // 初始值
})

const loading = ref(false)

// 需要转换为数字的 code 列表
const numericCodes = ['hit_max_number', 'hit_cache_days', 'task_pool_max']

const handleSave = async () => {
  loading.value = true
  // 保存逻辑
  await putSettingApi(form.value)
  ElMessage.success('操作成功')
  getSettingList()
  loading.value = false
}

const getSettingList = async () => {
  const { data } = await getSettingListApi()
  // 遍历 data 数组，将对应的 val 赋值给 form
  data.forEach((item) => {
    if (item.code in form.value) {
      // 检查是否需要转换为数字
      if (numericCodes.includes(item.code)) {
        form.value[item.code] = parseInt(item.val, 10) // 将字符串转换为数字
      } else {
        form.value[item.code] = item.val // 直接赋值
      }
    }
  })
}

onMounted(() => {
  getSettingList()
})
</script>

<style lang="scss" scoped>
/* 自定义样式 */
</style>
