<template>
  <div class="mt-4 flex flex-col items-center" style="min-height: 100%">
    <ElForm>
      <div class="flex flex-col items-center justify-center">
        <div>
          <ElRadioGroup v-model="searchType">
            <ElRadio :value="1" size="large">精确搜索</ElRadio>
            <ElRadio :value="2" size="large">前缀搜索</ElRadio>
            <ElRadio :value="3" size="large">后缀搜索</ElRadio>
            <ElRadio :value="5" size="large">模糊搜索</ElRadio>
          </ElRadioGroup>
          <ElInput
            v-model="keyword"
            placeholder="请输入关键词进行搜索"
            class="input-with-select"
            @keydown.enter="handleEnter"
          >
            <template #append>
              <ElButton size="large" :loading="loading" @click="handleSearch">搜索一下</ElButton>
            </template>
          </ElInput>
        </div>
      </div>
    </ElForm>

    <div style="width: 70%" v-if="searched">
      <ElCard v-loading="loading" class="mt-4">
        <template #header v-if="tableData.length > 0">
          <div
            class="card-header"
            style="display: flex; justify-content: space-between; align-items: center"
          >
            <div>
              <ElButton v-hasPermi="`/password/export`" type="primary" @click="exportExcel"
                >导出</ElButton
              >
            </div>
            <div>
              <ElButton
                v-hasPermi="`/password/autoLogin`"
                type="primary"
                @click="handleLogin"
                :loading="loginLoading"
                >自动登录</ElButton
              >
            </div>
          </div>
        </template>
        <div>
          <ElTable :data="tableData" border style="width: 100%">
            <ElTableColumn prop="_source.url" label="URL" />
            <ElTableColumn prop="_source.url_domain" label="Domain" />
            <ElTableColumn prop="_source.username" label="用户名" />
            <ElTableColumn prop="_source.password" label="密码" />
            <ElTableColumn label="操作" width="250">
              <template #default="{ row }">
                <ElButton
                  :icon="Position"
                  size="small"
                  type="primary"
                  @click="toDetail(row._source.cid)"
                  >CID详情</ElButton
                >
                <ShowPasswordBtn :row="row" />
              </template>
            </ElTableColumn>
          </ElTable>

          <div class="flex items-center mt-2">
            <ElSelect v-model="size" style="width: 128px" @change="sizeChange">
              <ElOption :value="10" label="10条/页" />
              <ElOption :value="50" label="50条/页" />
              <ElOption :value="100" label="100条/页" />
            </ElSelect>
            <ElButton
              type="primary"
              class="ml-2"
              :disabled="currentPage === 1"
              :icon="ArrowLeft"
              @click="prevClick"
            />
            <ElButton
              type="primary"
              class="ml-2"
              :icon="ArrowRight"
              :disabled="tableData.length < size"
              @click="nextClick"
            />
          </div>
        </div>
        <!-- <ElEmpty v-else description="暂无数据" /> -->
      </ElCard>

      <AutoLoginModule ref="autoLoginRef" />
    </div>
  </div>
</template>

<script setup>
import {
  getPasswordListAPI,
  exportPasswordListAPI,
  checkPasswordAPI,
  getPasswordAPI
} from '@/api/password'
import { createSearchRecordApi } from '@/api/searchRecord'
import { ArrowLeft, ArrowRight, Position, View } from '@element-plus/icons-vue'

import {
  ElInput,
  ElSelect,
  ElOption,
  ElButton,
  ElForm,
  ElMessage,
  ElRow,
  ElCol,
  ElCard,
  ElTable,
  ElRadioGroup,
  ElRadio,
  ElTableColumn,
  ElPagination,
  ElEmpty,
  ElNotification,
  ElMessageBox
} from 'element-plus'
import { onMounted, reactive, ref } from 'vue'
import { useRoute } from 'vue-router'
import { h } from 'vue'
import { useRouter } from 'vue-router'
import AutoLoginModule from '@/components/AutoLogin/index.vue'
import ShowPasswordBtn from '@/components/ShowPassword/index.vue'

const router = useRouter()

const searchType = ref(1)
const keyword = ref('')
const loading = ref(false)
const searched = ref(false)

const loginLoading = ref(false)

const currentPage = ref(1)
const size = ref(10)

const tableData = ref([])

const totalData = ref(0)

const handleEnter = (e) => {
  e.preventDefault()
}

const prevClick = () => {
  currentPage.value = currentPage.value - 1
  getPasswordList()
}
const nextClick = () => {
  currentPage.value = currentPage.value + 1
  getPasswordList()
}

const sizeChange = (val) => {
  currentPage.value = 1
  size.value = val
  getPasswordList()
}

const handleSizeChange = (number) => {
  size.value = number
  currentPage.value = 1

  getPasswordList()
}

const handleCurrentChange = (number) => {
  currentPage.value = number

  getPasswordList()
}

const handleSearch = () => {
  if (!keyword.value) {
    ElMessage.error('请填写您需要搜索的关键字')
    return
  }
  currentPage.value = 1
  toSearch()
}

const toSearch = async () => {
  if (!keyword.value) {
    ElMessage.error('请填写您需要搜索的关键字')
    return
  }
  if (keyword.value.length < 2) {
    ElMessage.error('关键字最小长度为2个字符')
    return
  }
  const apiParams = {
    keyword: keyword.value,
    esType: searchType.value,
    // searchType: [2],
    type: 2
  }

  // 记录日志
  createSearchRecordApi(apiParams)

  getPasswordList()
}

const toDetail = (cid) => {
  const href = '/home/<USER>/result/' + cid
  window.open(href, '_blank')
}

const autoLoginRef = ref()

const handleLogin = async () => {
  loginLoading.value = true
  try {
    const { data } = await getPasswordListAPI({
      keyword: keyword.value,
      searchType: searchType.value,
      paging: 0
    })
    const { items } = data || {}

    autoLoginRef.value.open(items)
  } finally {
    loginLoading.value = false
  }
}

const getPasswordList = async () => {
  searched.value = true
  loading.value = true

  try {
    const { data } = await getPasswordListAPI({
      keyword: keyword.value,
      searchType: searchType.value,
      page: currentPage.value,
      limit: size.value
    })
    const { total, items } = data || {}
    //totalData.value = total
    // tableData.value = items.map((i) => i._source)
    tableData.value = items
  } finally {
    loading.value = false
  }
}

const { query } = useRoute()
onMounted(() => {
  let { keyword: kv, esType } = query || {}
  if (kv && esType) {
    keyword.value = kv
    searchType.value = esType ? +esType : 1
    toSearch()
  }
})

const exportExcel = async () => {
  if (!keyword.value) {
    ElMessage.error('请填写您需要搜索的关键字')
    return
  }

  loading.value = true

  try {
    const response = await exportPasswordListAPI({
      keyword: keyword.value,
      searchType: searchType.value
    })

    ElNotification({
      type: 'success',
      title: '操作成功',
      message: h('div', null, [
        '导出成功，您可以点击',
        h(
          'a',
          {
            style: {
              color: '#409eff',
              cursor: 'pointer'
            },
            onClick: () => {
              router.push('/download-center/download-list')
            }
          },
          '下载中心'
        ),
        '来进行数据下载'
      ])
    })
  } finally {
    loading.value = false
  }
}
</script>

<style lang="scss" scoped></style>
