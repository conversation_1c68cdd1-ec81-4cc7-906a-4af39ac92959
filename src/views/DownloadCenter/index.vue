<script setup lang="tsx">
import { reactive, ref, unref, onMounted } from 'vue'
import { useTable } from '@/hooks/web/useTable'
import { Table } from '@/components/Table'
import { Search } from '@/components/Search'
import { ContentWrap } from '@/components/ContentWrap'
import { CrudSchema, useCrudSchemas } from '@/hooks/web/useCrudSchemas'
import { BaseButton } from '@/components/Button'

import { ElTag, ElDivider, ElMessageBox, ElMessage } from 'element-plus'
import { FormSchema } from '@/components/Form'
import { getAuthListAPI } from '@/api/user'

import { filterExportFileSource, filterExportFileStatus } from '@/utils/filter'
import {
  downloadFileApi,
  getExportFileListApi,
  checkExcelApi,
  deleteDownloadApi
} from '@/api/exportFile'
import { getPushConnection } from '@/utils/push'
import { useUserStore } from '@/store/modules/user'

const { tableRegister, tableState, tableMethods } = useTable({
  fetchDataApi: async () => {
    const { pageSize, currentPage } = tableState
    const res = await getExportFileListApi({
      page: unref(currentPage),
      size: unref(pageSize),
      ...unref(searchParams)
    })
    return {
      list: res.data.items || [],
      total: res.data.total
    }
  }
})

const { total, loading, dataList, pageSize, currentPage } = tableState
const { getList } = tableMethods

// 添加一个 Map 来跟踪每个按钮的 loading 状态
const downloadLoading = ref(new Map())

const crudSchemas = reactive<CrudSchema[]>([
  {
    field: 'selection',
    search: {
      hidden: true
    },
    form: {
      hidden: true
    },
    detail: {
      hidden: true
    },
    table: {
      type: 'selection'
    }
  },
  {
    field: 'index',
    label: '序号',
    type: 'index',
    search: {
      hidden: true
    }
  },
  {
    field: 'source',
    label: '来源',
    minWidth: '100',
    search: {
      hidden: true
    },
    slots: {
      default: (data: any) => {
        return (
          <>
            <ElTag type={filterExportFileSource(data.row.source, true)}>
              {filterExportFileSource(data.row.source)}
            </ElTag>
          </>
        )
      }
    }
  },
  {
    field: 'filename',
    label: '文件名',
    minWidth: '160',
    search: {
      hidden: true
    }
  },
  {
    field: 'hit_total',
    label: '命中数量',
    minWidth: '160',
    search: {
      hidden: true
    }
  },
  {
    field: 'status',
    label: '状态',
    search: {
      hidden: true
    },
    minWidth: '100',
    slots: {
      default: (data: any) => {
        return (
          <>
            <ElTag type={filterExportFileStatus(data.row.status, true)}>
              {filterExportFileStatus(data.row.status)}
            </ElTag>
          </>
        )
      }
    }
  },
  {
    field: 'created_at',
    label: '创建时间',
    minWidth: '160',
    search: {
      hidden: true
    }
  },

  {
    field: 'user.username',
    label: '操作人',
    minWidth: '120',
    search: {
      hidden: true
    }
  },

  {
    field: 'action',
    label: '操作',
    search: {
      hidden: true
    },
    fixed: 'right',
    width: '150',
    slots: {
      default: (data: any) => {
        const row = data.row
        return (
          <>
            {row.status === 2 && ( // 只在 status = 2 时显示下载按钮
              <BaseButton
                type="primary"
                size="small"
                loading={downloadLoading.value.get(row.id)}
                onClick={() => checkDownload(row)}
              >
                下载
              </BaseButton>
            )}
            <BaseButton type="danger" size="small" onClick={() => deleteRecord(row)}>
              删除
            </BaseButton>
          </>
        )
      }
    }
  }
])

const { allSchemas } = useCrudSchemas(crudSchemas)

const searchSchemas = reactive<FormSchema[]>([
  {
    field: 'user_id',
    label: '操作人',
    component: 'Select',
    componentProps: {
      nodeKey: 'id',
      props: {
        value: 'id',
        label: 'name'
      }
    },
    optionApi: async () => {
      const { data } = await getAuthListAPI()
      return data
    }
  },
  {
    field: 'created_at',
    label: '创建时间',
    component: 'DatePicker',
    componentProps: {
      type: 'datetimerange',
      valueFormat: 'YYYY-MM-DD HH:mm:ss'
    }
  },
  {
    field: 'source',
    label: '来源',
    component: 'Select',
    componentProps: {
      options: [
        {
          label: '密码搜索',
          value: 1
        }
      ]
    }
  }
])

const searchParams = ref({})
const setSearchParams = (data: any) => {
  searchParams.value = data
  getList()
}

const checkDownload = async (row) => {
  const { data } = await checkExcelApi(row.id)
  const { is_paid, message } = data || {}
  if (is_paid === 0) {
    ElMessageBox.confirm(message, '提示', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    })
      .then(() => {
        download(row)
      })
      .catch(() => {})
  } else {
    download(row)
  }
}

const download = async (row) => {
  // 设置当前行的下载按钮为 loading 状态
  downloadLoading.value.set(row.id, true)
  try {
    const response = (await downloadFileApi(row.id)) as any
    // 创建一个 Blob 对象
    const blob = new Blob([response.data], { type: 'text/csv' })

    // 从响应头中提取文件名
    const contentDisposition = response.headers['content-disposition']
    const filename = contentDisposition.split('filename=')[1].replace(/"/g, '')

    // 创建一个下载链接
    const link = document.createElement('a')
    link.href = window.URL.createObjectURL(blob)
    link.download = decodeURI(filename)

    // 模拟点击下载链接
    document.body.appendChild(link)
    link.click()

    // 移除下载链接
    document.body.removeChild(link)
  } catch (e: any) {
    const jsonData = await e.response.data.text()
    const parsedData = JSON.parse(jsonData) // 解析 JSON
    const { msg } = parsedData || {}
    ElMessage.error(msg)
  } finally {
    // 无论成功还是失败，都取消 loading 状态
    downloadLoading.value.set(row.id, false)
  }
}

const listenMessage = () => {
  const connection = getPushConnection()
  const userStore = useUserStore()
  // 创建监听频道
  const channel = connection.subscribe('password-export-' + userStore.getUserInfo.id)
  channel.on('finished', function () {
    getList()
  })
}

const deleteRecord = async (row: any = null) => {
  ElMessageBox.confirm('是否删除此条记录', '提示', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning'
  })
    .then(async () => {
      const id = row.id
      await deleteDownloadApi(id).finally(() => {})

      getList()
    })
    .catch(() => {})
}

onMounted(() => {
  listenMessage()
})
</script>

<template>
  <ContentWrap>
    <Search :schema="searchSchemas" @reset="setSearchParams" @search="setSearchParams" />

    <ElDivider />

    <Table
      v-model:current-page="currentPage"
      v-model:page-size="pageSize"
      :columns="allSchemas.tableColumns"
      :data="dataList"
      :loading="loading"
      @register="tableRegister"
      :pagination="{
        total
      }"
    />
  </ContentWrap>
</template>
