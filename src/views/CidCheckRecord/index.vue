<script setup lang="tsx">
import { reactive, ref, unref } from 'vue'
import { getCidCheckRecordApi } from '@/api/cidCheckRecord'
import { useTable } from '@/hooks/web/useTable'
import { Table } from '@/components/Table'
import { Search } from '@/components/Search'
import { ContentWrap } from '@/components/ContentWrap'
import { CrudSchema, useCrudSchemas } from '@/hooks/web/useCrudSchemas'
import { BaseButton } from '@/components/Button'
import { FormSchema } from '@/components/Form'
import { getAuthListAPI } from '@/api/user'

const { tableRegister, tableState, tableMethods } = useTable({
  fetchDataApi: async () => {
    const { pageSize, currentPage } = tableState
    const res = await getCidCheckRecordApi({
      page: unref(currentPage),
      size: unref(pageSize),
      ...unref(searchParams)
    })
    return {
      list: res.data.items || [],
      total: res.data.total
    }
  }
})

const { total, loading, dataList, pageSize, currentPage } = tableState
const { getList } = tableMethods

const crudSchemas = reactive<CrudSchema[]>([
  {
    field: 'selection',
    search: {
      hidden: true
    },
    form: {
      hidden: true
    },
    detail: {
      hidden: true
    },
    table: {
      type: 'selection'
    }
  },
  {
    field: 'index',
    label: '序号',
    type: 'index',
    search: {
      hidden: true
    }
  },
  {
    field: 'cid',
    label: 'CID'
  },
  {
    field: 'user.username',
    label: '查看人',
    search: {
      hidden: true
    }
  },
  {
    field: 'created_at',
    label: '查看时间',
    search: {
      hidden: true
    }
  },
  {
    field: 'action',
    label: '操作',
    width: 240,
    search: {
      hidden: true
    },
    slots: {
      default: (data: any) => {
        const row = data.row
        return (
          <>
            <BaseButton type="primary" onClick={() => searchTo(row)} size="small">
              再次查看
            </BaseButton>
          </>
        )
      }
    }
  }
])

const { allSchemas } = useCrudSchemas(crudSchemas)

const searchSchemas = reactive<FormSchema[]>([
  {
    field: 'cid',
    label: 'CID',
    component: 'Input'
  },
  {
    field: 'user_id',
    label: '查看人',
    component: 'Select',
    componentProps: {
      nodeKey: 'id',
      props: {
        value: 'id',
        label: 'name'
      }
    },
    optionApi: async () => {
      const { data } = await getAuthListAPI()
      return data
    }
  },
  {
    field: 'time',
    label: '查看时间',
    component: 'DatePicker',
    componentProps: {
      type: 'datetimerange',
      valueFormat: 'YYYY-MM-DD HH:mm:ss'
    }
  }
])

const searchParams = ref({})
const setSearchParams = (data: any) => {
  searchParams.value = data
  getList()
}

const searchTo = (row) => {
  const href = '/home/<USER>/result/' + row.cid
  window.open(href, '_blank')
}
</script>

<template>
  <ContentWrap>
    <Search :schema="searchSchemas" @reset="setSearchParams" @search="setSearchParams" />
    <Table
      v-model:current-page="currentPage"
      v-model:page-size="pageSize"
      :columns="allSchemas.tableColumns"
      :data="dataList"
      :loading="loading"
      @register="tableRegister"
      :pagination="{
        total
      }"
    />
  </ContentWrap>
</template>
