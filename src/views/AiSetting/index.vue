<template>
  <div class="container mx-auto">
    <div class="flex justify-center">
      <ElCard header="AI 设置" class="w-full">
        <ElDescriptions border :column="1">
          <ElDescriptionsItem label="请求地址">
            <ElInput v-model="form.ai_request_url" />
          </ElDescriptionsItem>
          <ElDescriptionsItem label="API Key">
            <ElInput v-model="form.ai_api_key" />
          </ElDescriptionsItem>
        </ElDescriptions>
        <template #footer>
          <ElButton type="primary" :loading="loading" @click="handleSave"> 保存 </ElButton>
        </template>
      </ElCard>
    </div>
  </div>
</template>

<script setup>
import { getSettingListApi, putSettingApi } from '@/api/setting'
import {
  ElCard,
  ElDescriptions,
  ElDescriptionsItem,
  ElButton,
  ElInput,
  ElMessage
} from 'element-plus'
import { onMounted, reactive, ref } from 'vue'

const form = ref({
  ai_request_url: '',
  ai_api_key: ''
})

const loading = ref(false)

// 需要转换为数字的 code 列表
const numericCodes = []

const handleSave = async () => {
  loading.value = true
  // 保存逻辑
  await putSettingApi(form.value)
  ElMessage.success('操作成功')
  getSettingList()
  loading.value = false
}

const getSettingList = async () => {
  const { data } = await getSettingListApi()
  // 遍历 data 数组，将对应的 val 赋值给 form
  data.forEach((item) => {
    if (item.code in form.value) {
      // 检查是否需要转换为数字
      if (numericCodes.includes(item.code)) {
        form.value[item.code] = parseInt(item.val, 10) // 将字符串转换为数字
      } else {
        form.value[item.code] = item.val // 直接赋值
      }
    }
  })
}

onMounted(() => {
  getSettingList()
})
</script>

<style lang="scss" scoped>
/* 自定义样式 */
</style>
