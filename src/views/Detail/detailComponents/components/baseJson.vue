<template>
  <JsonEditor
    class="json-item"
    v-for="(i, k) in list"
    :key="k"
    :modelValue="parseJsonValue(i._source[field])"
  />
</template>

<script setup>
import { JsonEditor } from '@/components/JsonEditor'
import { onMounted, ref, watch, inject, onUnmounted } from 'vue'
import { searchItemAPI } from '@/api/search'

const props = defineProps({
  cid: {
    type: String,
    default: null
  },
  field: {
    type: String,
    default: null
  },
  searchType: {
    type: Number,
    default: null
  },
  searchFrom: {
    type: Number,
    default: null
  },
  keyword: {
    type: String,
    default: null
  },
  apiHost: {
    type: Function,
    default: null
  }
})

const list = ref([])
const loading = ref(false)

// 解析 JSON 值的函数
const parseJsonValue = (value) => {
  if (!value) {
    return {}
  }

  // 如果已经是对象，直接返回
  if (typeof value === 'object') {
    return value
  }

  // 如果是字符串，尝试解析为 JSON
  if (typeof value === 'string') {
    try {
      const parsed = JSON.parse(value)
      return parsed
    } catch (error) {
      // 如果解析失败，将字符串包装为对象
      return {
        content: value
      }
    }
  }

  // 其他类型，包装为对象
  return {
    value: value,
    type: typeof value
  }
}

const getData = async (params = {}) => {
  loading.value = true
  const apiName = props.apiHost || searchItemAPI
  const res = await apiName({
    cid: props.cid,
    search_from: props.searchFrom,
    // search_type: props.searchType,
    keyword: props.keyword,
    ...params
  })

  loading.value = false
  list.value = res.data.items
}

const eventBus = inject('eventBus')

onMounted(async () => {
  await getData()

  // 监听刷新事件
  eventBus.on('refreshData', (params) => {
    getData(params)
  })
})

// 组件卸载时移除事件监听
onUnmounted(() => {
  eventBus.off('refreshData')
})
</script>

<style scoped>
.json-item {
  max-height: 300px;
  overflow-y: auto;
}
</style>
