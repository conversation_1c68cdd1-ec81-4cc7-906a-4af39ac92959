<template>
  <div>
    <Table
      :columns="processedColumns"
      :data="list"
      :loading="loading"
      :showPagination="showPagination"
      :pagination="{
        total: pagination.total,
        background: pagination.background,
        pageSizes: [10, 500, 1000]
      }"
      :imagePreview="imagePreview"
      v-model:pageSize="pagination.pageSize"
      v-model:currentPage="pagination.currentPage"
      @update:page-size="getData"
      @update:current-page="getData"
      @row-dblclick="handleRowDblClick"
    />
  </div>
</template>

<script setup>
import { Table } from '@/components/Table'
import { onMounted, ref, reactive, inject, onUnmounted, computed, h } from 'vue'
import { searchItemAPI } from '@/api/search'
import { useClipboard } from '@vueuse/core'
import { ElMessage, ElPopover, ElButton } from 'element-plus'
import { get } from 'lodash-es'
import { extend } from 'dayjs'

const props = defineProps({
  cid: {
    type: String,
    default: null
  },
  columns: {
    type: Array,
    default: () => []
  },
  searchType: {
    type: Number,
    default: null
  },
  searchFrom: {
    type: Number,
    default: null
  },
  apiHost: {
    type: Function,
    default: null
  },
  showPagination: {
    type: Boolean,
    default: () => true
  },
  imagePreview: {
    type: Array,
    default: () => []
  },
  extendItems: {
    type: Array,
    default: () => []
  },
  keyword: {
    type: String,
    default: null
  }
})

const list = ref([])
const loading = ref(false)

const pagination = ref({
  pageSize: 10,
  total: 0,
  currentPage: 1
})

const storeParams = ref({})

// 高亮关键词的函数
const highlightKeyword = (text, keyword) => {
  if (!keyword || !text) {
    return text
  }

  // 如果不是字符串，尝试转换为字符串
  const textStr = typeof text === 'string' ? text : String(text)

  // 创建正则表达式，忽略大小写
  const regex = new RegExp(`(${keyword.replace(/[.*+?^${}()|[\]\\]/g, '\\$&')})`, 'gi')

  // 如果没有匹配，直接返回原文本
  if (!regex.test(textStr)) {
    return text
  }

  // 重置正则表达式的lastIndex
  regex.lastIndex = 0

  // 分割文本并高亮匹配的部分
  const parts = textStr.split(regex)

  return h(
    'span',
    parts.map((part, index) => {
      if (part && regex.test(part)) {
        // 重置正则表达式的lastIndex
        regex.lastIndex = 0
        return h(
          'span',
          {
            key: index,
            style: {
              backgroundColor: '#ffeb3b',
              color: '#000',
              fontWeight: 'bold'
            }
          },
          part
        )
      }
      return part
    })
  )
}

// 递归处理文本内容的高亮函数
const highlightTextInContent = (content, keyword) => {
  if (!keyword || !content) {
    return content
  }

  // 如果是字符串，直接高亮
  if (typeof content === 'string') {
    return highlightKeyword(content, keyword)
  }

  // 如果是数字或其他基本类型，转换为字符串后高亮
  if (typeof content === 'number' || typeof content === 'boolean') {
    return highlightKeyword(String(content), keyword)
  }

  // 如果是Vue的VNode或JSX，尝试提取文本内容
  if (content && typeof content === 'object') {
    // 如果有children属性，递归处理
    if (content.children) {
      if (Array.isArray(content.children)) {
        return {
          ...content,
          children: content.children.map((child) => highlightTextInContent(child, keyword))
        }
      } else {
        return {
          ...content,
          children: highlightTextInContent(content.children, keyword)
        }
      }
    }

    // 如果有props.children，递归处理
    if (content.props && content.props.children) {
      return {
        ...content,
        props: {
          ...content.props,
          children: highlightTextInContent(content.props.children, keyword)
        }
      }
    }
  }

  return content
}

// 处理columns，为每个字段添加高亮功能
const processedColumns = computed(() => {
  if (!props.keyword) {
    return props.columns
  }

  return props.columns.map((column) => {
    // 如果已经有自定义的slots，需要包装原有的slots来添加高亮功能
    if (column.slots && column.slots.default) {
      const originalSlot = column.slots.default
      return {
        ...column,
        slots: {
          ...column.slots,
          default: (data) => {
            const originalResult = originalSlot(data)
            // 尝试对结果进行高亮处理
            return highlightTextInContent(originalResult, props.keyword)
          }
        }
      }
    }

    // 为没有自定义slots的字段添加高亮功能
    return {
      ...column,
      slots: {
        default: ({ row }) => {
          const value = get(row, column.field)
          return highlightKeyword(value, props.keyword)
        }
      }
    }
  })
})

const getData = async () => {
  loading.value = true
  const apiName = props.apiHost || searchItemAPI
  const params = storeParams.value || {}
  const res = await apiName({
    cid: props.cid,
    from: (pagination.value.currentPage - 1) * pagination.value.pageSize,
    size: pagination.value.pageSize,
    search_from: props.searchFrom,
    keyword: props.keyword,
    // search_type: props.searchType,
    ...params
  })
  if (pagination.value.currentPage === 1 && props.extendItems.length) {
    list.value = [...props.extendItems, ...res.data.items]
  } else {
    list.value = res.data.items
  }
  loading.value = false
  pagination.value.total = res.data.total || 0
}

const eventBus = inject('eventBus')

onMounted(async () => {
  await getData()

  // 监听刷新事件
  eventBus.on('refreshData', (params) => {
    storeParams.value = params
    getData()
  })
})

// 组件卸载时移除事件监听
onUnmounted(() => {
  eventBus.off('refreshData')
})

// 右键菜单的状态
const isMenuVisible = ref(false)
const menuPosition = reactive({ x: 0, y: 0 })
let selectedRow = ref(null)
const popover = ref(null)

// 复制内容到剪贴板
const copyContent = async (content) => {
  const { copy, copied, isSupported } = useClipboard({
    source: content,
    legacy: true
  })
  if (!isSupported) {
    ElMessage.error('复制失败')
  } else {
    await copy()
    if (copied.value) {
      ElMessage.success('复制成功')
    }
  }
}

const handleRowDblClick = (row, column) => {
  // 获取列的字段名
  const fieldName = column.property
  // 动态获取该字段对应的值
  const selectedValue = get(row, fieldName)
  copyContent(selectedValue)
}
</script>

<style scoped>
.custom-menu {
  z-index: 1000;
  transform-origin: top left; /* 确保菜单从点击位置展开 */
}
</style>
