<template>
  <BaseTable
    :columns="columns"
    :show-pagination="false"
    :cid="cid"
    :api-host="searchAccountAPI"
    :keyword="keyword"
  />
</template>

<script setup>
import BaseTable from './components/baseTable.vue'
import { searchAccountAPI } from '@/api/search'
const props = defineProps({
  cid: {
    type: String,
    default: null
  },
  keyword: {
    type: String,
    default: null
  }
})

const columns = [
  {
    field: 'account',
    label: '账号'
  },
  {
    field: 'passwordNum',
    label: '在密码模块匹配数量'
  },
  {
    field: 'autofillNum',
    label: '在自动填充模块匹配数量'
  }
]
</script>
