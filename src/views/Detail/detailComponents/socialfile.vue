<template>
  <ElCard :header="header">
    <ElTabs v-if="isMatch" v-model="activeTab">
      <ElTabPane label="搜索命中" name="searchHit">
        <BaseTable
          :columns="columns"
          :search-from="searchFrom"
          :cid="cid"
          :keyword="taskKeyword"
          :searchType="searchType"
        />
      </ElTabPane>
      <ElTabPane label="全量" name="all">
        <BaseTable
          :columns="columns"
          :search-from="searchFrom"
          :cid="cid"
          :keyword="keyword"
          :searchType="searchType"
        />
      </ElTabPane>
    </ElTabs>

    <BaseTable
      v-else
      :columns="columns"
      :search-from="searchFrom"
      :cid="cid"
      :keyword="keyword"
      :searchType="searchType"
    />
  </ElCard>
</template>

<script setup lang="tsx">
import { computed, ref } from 'vue'
import BaseTable from './components/baseTable.vue'
import { ElButton, ElCard, <PERSON><PERSON><PERSON><PERSON>, ElTabPane } from 'element-plus'
import { downloadRes } from '@/utils/download'

const props = defineProps({
  cid: {
    type: String,
    default: null
  },
  keyword: {
    type: String,
    default: null
  },
  searchType: {
    type: Number,
    default: null
  },
  header: {
    type: String,
    default: ''
  },
  searchFrom: {
    type: Number,
    default: 10
  },
  matchModules: {
    type: Array,
    default: () => []
  }
})

const activeTab = ref('searchHit')

const isMatch = computed(() => {
  return props.matchModules.some((item: any) => item.search_type == props.searchFrom)
})

const taskKeyword = computed(() => {
  const matchedModule = props.matchModules.find((item: any) => item.search_type == props.searchFrom)
  return matchedModule ? (matchedModule as any).keyword : undefined
})

const columns = [
  {
    field: '_source.filename',
    label: '文件名'
  },
  {
    label: '操作',
    slots: {
      default: (data) => {
        return (
          <ElButton link type="primary" onClick={() => operate(data)}>
            下载
          </ElButton>
        )
      }
    }
  }
]

const operate = async ({ row }) => {
  const params = {
    es_id: row._id,
    es_index: row._index
  }
  downloadRes(params)
}
</script>
