<template>
  <ElCard :header="header">
    <template #header>
      <div class="flex justify-between items-center">
        <div>{{ header }}</div>
        <div>
          <ElButton
            v-hasPermi="`/cid/autoLogin`"
            v-if="isTestData"
            size="small"
            type="primary"
            @click="handleLogin"
            >自动登录</ElButton
          >
        </div>
      </div>
    </template>
    <ElTabs v-if="isMatch" v-model="activeTab">
      <ElTabPane label="搜索命中" name="searchHit">
        <BaseTable
          :columns="columns"
          :search-from="searchFrom"
          :cid="cid"
          :keyword="taskKeyword"
          :searchType="searchType"
          :extend-items="extendItems"
        />
      </ElTabPane>
      <ElTabPane label="全量" name="all">
        <BaseTable
          :columns="columns"
          :search-from="searchFrom"
          :cid="cid"
          :keyword="keyword"
          :searchType="searchType"
          :extend-items="extendItems"
        />
      </ElTabPane>
    </ElTabs>

    <BaseTable
      v-else
      :columns="columns"
      :search-from="searchFrom"
      :cid="cid"
      :keyword="keyword"
      :searchType="searchType"
      :extend-items="extendItems"
    />

    <AutoLoginModule ref="autoLoginRef" />
  </ElCard>
</template>

<script setup lang="tsx">
import { computed, ref } from 'vue'
import BaseTable from './components/baseTable.vue'
import { ElCard, ElButton, ElTabs, ElTabPane } from 'element-plus'
import AutoLoginModule from '@/components/AutoLogin/index.vue'
import ShowPasswordBtn from '@/components/ShowPassword/index.vue'

const props = defineProps({
  cid: {
    type: String,
    default: null
  },
  keyword: {
    type: String,
    default: null
  },
  searchType: {
    type: Number,
    default: null
  },
  header: {
    type: String,
    default: ''
  },
  searchFrom: {
    type: Number,
    default: null
  },
  matchModules: {
    type: Array,
    default: () => []
  }
})

const activeTab = ref('searchHit')

const isMatch = computed(() => {
  return props.matchModules.some((item: any) => item.search_type == props.searchFrom)
})

const taskKeyword = computed(() => {
  const matchedModule = props.matchModules.find((item: any) => item.search_type == props.searchFrom)
  return matchedModule ? (matchedModule as any).keyword : undefined
})

const columns = [
  {
    field: '_source.url_domain',
    label: '域名'
  },
  {
    field: '_source.url',
    label: '站点'
  },
  {
    field: '_source.username',
    slots: {
      default: ({ row }) => {
        return (
          <div>
            {row._source.user_suffix
              ? row._source.username + '@' + row._source.user_suffix
              : row._source.username}
          </div>
        )
      }
    },
    label: '用户名'
  },
  {
    field: '_source.password',
    label: '密码'
  },
  {
    field: '_source.app',
    label: '所在 App'
  }
  // {
  //   label: '操作',
  //   slots: {
  //     default: ({ row }) => {
  //       return <ShowPasswordBtn row={row} />
  //     }
  //   }
  // }
]

const autoLoginRef = ref()

const handleLogin = () => {
  autoLoginRef.value.open(extendItems.value, props.cid)
}

const isTestData = computed(() => {
  return props.cid === '4f5e480578508395158435f03b5d8cc1'
})

const extendItems = computed(() => {
  if (isTestData.value) {
    return [
      {
        _id: 'CyBSPZMB1',
        _source: {
          cid: '4f5e480578508395158435f03b5d8cc1',
          url: 'https://protonmail.com',
          username: '<EMAIL>',
          password: 'HGSDGAEBsa!@333%'
        }
      },
      {
        _id: 'CyBSPZMB2',
        _source: {
          cid: '4f5e480578508395158435f03b5d8cc1',
          url: 'https://www.gmx.com',
          username: '<EMAIL>',
          password: 'Aa12345678'
        }
      },
      {
        _id: 'CyBSPZMB3',
        _source: {
          cid: '4f5e480578508395158435f03b5d8cc1',
          url: 'https://gmail.com',
          username: '<EMAIL>',
          password: 'lequangnghia'
        }
      },
      {
        _id: 'CyBSPZMB4',
        _source: {
          cid: '4f5e480578508395158435f03b5d8cc1',
          url: 'https://email.163.com',
          username: '<EMAIL>',
          password: 'fangz0696ouxiya'
        }
      },
      {
        _id: 'CyBSPZMB5',
        _source: {
          cid: '4f5e480578508395158435f03b5d8cc1',
          url: 'https://yahoo.com',
          username: '<EMAIL>',
          password: 'wj198875@#!'
        }
      },
      {
        _id: 'CyBSPZMB6',
        _source: {
          cid: '4f5e480578508395158435f03b5d8cc1',
          url: 'https://outlook.com',
          username: '<EMAIL>',
          password: 'HSAHD121!#s121,'
        }
      },
      {
        _id: 'CyBSPZMB7',
        _source: {
          cid: '4f5e480578508395158435f03b5d8cc1',
          url: 'https://wpvulndb.com/users',
          username: '<EMAIL>',
          password: 'asdha765$#SA12'
        }
      },
      {
        _id: 'CyBSPZMB8',
        _source: {
          cid: '4f5e480578508395158435f03b5d8cc1',
          url: 'https://main.whoisxmlapi.com/login',
          username: '<EMAIL>',
          password: 'lip52693'
        }
      }
    ]
  }
  return []
})
</script>
