<template>
  <ElCard :header="header">
    <ElTabs v-if="isMatch" v-model="activeTab">
      <ElTabPane label="搜索命中" name="searchHit">
        <BaseTable
          :columns="columns"
          :search-from="searchFrom"
          :cid="cid"
          :keyword="taskKeyword"
          :searchType="searchType"
        />
      </ElTabPane>
      <ElTabPane label="全量" name="all">
        <BaseTable
          :columns="columns"
          :search-from="searchFrom"
          :cid="cid"
          :keyword="keyword"
          :searchType="searchType"
        />
      </ElTabPane>
    </ElTabs>

    <BaseTable
      v-else
      :columns="columns"
      :search-from="searchFrom"
      :cid="cid"
      :keyword="keyword"
      :searchType="searchType"
    />
  </ElCard>
</template>

<script setup>
import { ref, watch, inject, computed } from 'vue'
import BaseTable from './components/baseTable.vue'
import { ElCard, ElTabs, ElTabPane } from 'element-plus'

const props = defineProps({
  cid: {
    type: String,
    required: true
  },
  keyword: {
    type: String,
    default: ''
  },
  searchType: {
    type: Number,
    default: 1
  },
  header: {
    type: String,
    default: ''
  },
  searchFrom: {
    type: Number,
    default: 8
  },
  matchModules: {
    type: Array,
    default: () => []
  }
})

const activeTab = ref('searchHit')

const isMatch = computed(() => {
  return props.matchModules.some((item) => item.search_type == props.searchFrom)
})

const taskKeyword = computed(() => {
  const matchedModule = props.matchModules.find((item) => item.search_type == props.searchFrom)
  return matchedModule ? matchedModule.keyword : undefined
})

const columns = [
  {
    field: '_source.url',
    label: '站点'
  },
  {
    field: '_source.title',
    label: '标题'
  },
  {
    field: '_source.accessTime',
    label: '访问时间'
  },
  {
    field: '_source.profile',
    label: '所在文件'
  }
]
</script>
