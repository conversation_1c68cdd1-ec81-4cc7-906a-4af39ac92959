<template>
  <ElCard :header="header">
    <ElTabs v-if="isMatch" v-model="activeTab">
      <ElTabPane label="搜索命中" name="searchHit">
        <BaseTable
          :columns="columns"
          :search-from="searchFrom"
          :cid="cid"
          :keyword="taskKeyword"
          :searchType="searchType"
        />
      </ElTabPane>
      <ElTabPane label="全量" name="all">
        <BaseTable
          :columns="columns"
          :search-from="searchFrom"
          :cid="cid"
          :keyword="keyword"
          :searchType="searchType"
        />
      </ElTabPane>
    </ElTabs>

    <BaseTable
      v-else
      :columns="columns"
      :search-from="searchFrom"
      :cid="cid"
      :keyword="keyword"
      :searchType="searchType"
    />
  </ElCard>
</template>

<script setup>
import { computed, ref } from 'vue'
import BaseTable from './components/baseTable.vue'
import { ElCard, ElTabs, ElTabPane } from 'element-plus'

const props = defineProps({
  cid: {
    type: String,
    default: null
  },
  keyword: {
    type: String,
    default: null
  },
  searchType: {
    type: Number,
    default: null
  },
  header: {
    type: String,
    default: ''
  },
  searchFrom: {
    type: Number,
    default: 9
  },
  matchModules: {
    type: Array,
    default: () => []
  }
})

const activeTab = ref('searchHit')

const isMatch = computed(() => {
  return props.matchModules.some((item) => item.search_type == props.searchFrom)
})

const taskKeyword = computed(() => {
  const matchedModule = props.matchModules.find((item) => item.search_type == props.searchFrom)
  return matchedModule ? matchedModule.keyword : undefined
})

const columns = [
  {
    field: '_source.holder',
    label: '持有者'
  },
  {
    field: '_source.cardtype',
    label: '卡片类型'
  },
  {
    field: '_source.expire',
    label: '过期时间'
  }
]
</script>
