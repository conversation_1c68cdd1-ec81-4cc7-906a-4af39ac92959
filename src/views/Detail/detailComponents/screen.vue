<template>
  <ElCard :header="header">
    <BaseTable
      :columns="columns"
      :search-from="7"
      :cid="cid"
      :keyword="keyword"
      :searchType="searchType"
      :image-preview="['_source.image_content']"
    />
  </ElCard>
</template>

<script setup lang="tsx">
import BaseTable from './components/baseTable.vue'
import { ElCard } from 'element-plus'

import { ElButton } from 'element-plus'
import { downloadRes } from '@/utils/download'

defineProps({
  cid: {
    type: String,
    default: null
  },
  keyword: {
    type: String,
    default: null
  },
  searchType: {
    type: Number,
    default: null
  },
  header: {
    type: String,
    default: ''
  }
})

const columns = [
  // {
  //   field: '_source.filename',
  //   label: '文件名'
  // },
  {
    field: '_source.image_content',
    label: '图片'
  },
  {
    label: '操作',
    slots: {
      default: (data) => {
        return (
          <ElButton link type="primary" onClick={() => operate(data)}>
            下载
          </ElButton>
        )
      }
    }
  }
]

const operate = async ({ row }) => {
  const params = {
    es_id: row._id,
    es_index: row._index
  }
  downloadRes(params)
}
</script>
