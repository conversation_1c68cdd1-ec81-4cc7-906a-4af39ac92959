<template>
  <ElCard :header="header">
    <BaseTable
      :columns="columns"
      :search-from="11"
      :cid="cid"
      :keyword="keyword"
      :searchType="searchType"
    />
  </ElCard>
</template>

<script setup lang="tsx">
import BaseTable from './components/baseTable.vue'

import { ElButton, ElCard } from 'element-plus'
import { downloadRes } from '@/utils/download'

defineProps({
  cid: {
    type: String,
    default: null
  },
  keyword: {
    type: String,
    default: null
  },
  searchType: {
    type: Number,
    default: null
  },
  header: {
    type: String,
    default: ''
  }
})

const columns = [
  {
    field: '_source.filename',
    label: '文件名'
  },
  {
    label: '操作',
    slots: {
      default: (data) => {
        return (
          <ElButton link type="primary" onClick={() => operate(data)}>
            下载
          </ElButton>
        )
      }
    }
  }
]

const operate = async ({ row }) => {
  const params = {
    es_id: row._id,
    es_index: row._index
  }
  downloadRes(params)
}
</script>
