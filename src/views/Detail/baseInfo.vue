<template>
  <ContentWrap title="基本信息" v-for="(sysinfo, key) in sysInfoList" :key="key">
    <ElDescriptions border>
      <ElDescriptionsItem label="IP">{{ sysinfo._source.IP_Address }}</ElDescriptionsItem>
      <ElDescriptionsItem label="国家/地区">{{
        filterCountry(sysinfo._source.Country)
      }}</ElDescriptionsItem>
      <ElDescriptionsItem label="语言">{{
        filterLanguage(sysinfo._source.Language)
      }}</ElDescriptionsItem>
      <ElDescriptionsItem label="用户名">{{ sysinfo._source.user }}</ElDescriptionsItem>
      <ElDescriptionsItem label="操作系统">{{ sysinfo._source.OSVersion }}</ElDescriptionsItem>
      <ElDescriptionsItem label="CPU">{{ sysinfo._source.CPU_Name }}</ElDescriptionsItem>
      <ElDescriptionsItem label="GPU">{{ sysinfo._source.GPU_name }}</ElDescriptionsItem>
      <ElDescriptionsItem label="硬件ID">{{ sysinfo._source.HardWareID }}</ElDescriptionsItem>
      <ElDescriptionsItem label="内存">{{ sysinfo._source.Memory }}</ElDescriptionsItem>
      <ElDescriptionsItem label="分辨率">{{ sysinfo._source.Screen_Resoluton }}</ElDescriptionsItem>
      <ElDescriptionsItem label="工作组">{{ sysinfo._source.Workgroup }}</ElDescriptionsItem>
      <ElDescriptionsItem label="电脑名称">{{
        sysinfo._source.ComputerNameNetBIOS
      }}</ElDescriptionsItem>
      <ElDescriptionsItem label="Domain">{{ sysinfo._source.domain }}</ElDescriptionsItem>
      <ElDescriptionsItem label="PC">{{ sysinfo._source.pc }}</ElDescriptionsItem>
      <ElDescriptionsItem label="数据记录时间">{{ sysinfo._source.Log_data }}</ElDescriptionsItem>
    </ElDescriptions>
  </ContentWrap>
</template>

<script setup>
import { ContentWrap } from '@/components/ContentWrap'
import {
  ElDescriptions,
  ElDescriptionsItem,
  ElRow,
  ElCol,
  ElCard,
  ElDivider,
  ElAnchor,
  ElAnchorLink,
  ElTag,
  ElAffix
} from 'element-plus'
import { onMounted, ref, watch } from 'vue'
import { searchSysinfoAPI } from '@/api/search'
import { filterCountry, filterLanguage } from '@/utils/filter'

const props = defineProps({
  cid: {
    type: String,
    default: null
  }
})

const sysInfoList = ref([])

const getSysinfo = async () => {
  const sysInfoRes = await searchSysinfoAPI({
    keyword: props.cid
  })
  sysInfoList.value = sysInfoRes?.data?.items
}

onMounted(async () => {
  await Promise.all([getSysinfo()])
})
</script>
