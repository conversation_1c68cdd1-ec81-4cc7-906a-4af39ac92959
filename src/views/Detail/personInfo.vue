<template>
  <div>
    <ElDrawer v-model="show" title="个人信息" size="40%">
      <!-- 空数据提示 -->
      <div v-if="!personData || personData.length === 0" class="empty-data">
        <ElEmpty description="暂无数据" />
      </div>

      <!-- 有数据时显示内容 -->
      <ElTabs v-else>
        <ElTabPane v-for="(person, index) in personData" :key="index" :label="person.name">
          <ElDescriptions title="Profile" border :column="1">
            <ElDescriptionsItem label="First Name">{{
              person.profile?.first_name
            }}</ElDescriptionsItem>
            <ElDescriptionsItem label="Last Name">
              {{ person.profile?.last_name }}
            </ElDescriptionsItem>
            <ElDescriptionsItem label="Country">
              {{ person.profile?.country }}
            </ElDescriptionsItem>
            <ElDescriptionsItem label="Current Employer">
              {{ person.profile?.current_employer }}
            </ElDescriptionsItem>
            <ElDescriptionsItem label="Location">
              {{ person.profile?.location }}
            </ElDescriptionsItem>
            <ElDescriptionsItem label="Position">
              {{ person.profile?.position }}
            </ElDescriptionsItem>
          </ElDescriptions>
          <ElCard header="Work" class="mt-5">
            <ElTable :data="person.work">
              <ElTableColumn label="Company" prop="company" />
              <ElTableColumn label="Company Trans" prop="company_trans" />
              <ElTableColumn label="Position" prop="position" />
              <ElTableColumn label="Position Trans" prop="position_trans" />
            </ElTable>
          </ElCard>

          <ElCard header="Education" class="mt-5">
            <ElTable :data="person.education">
              <ElTableColumn label="College" prop="college" />
              <ElTableColumn label="Major" prop="major" />

              <ElTableColumn label="Start Date" prop="start_date">
                <template #default="{ row }">
                  {{ row.start_date ? dayjs(row.start_date).format('YYYY-MM-DD') : '' }}
                </template>
              </ElTableColumn>

              <ElTableColumn label="End Date" prop="end_date">
                <template #default="{ row }">
                  {{ row.end_date ? dayjs(row.end_date).format('YYYY-MM-DD') : '' }}
                </template>
              </ElTableColumn>
            </ElTable>
          </ElCard>
        </ElTabPane>
      </ElTabs>
    </ElDrawer>
  </div>
</template>

<script setup>
import { ref, defineExpose } from 'vue'

import dayjs from 'dayjs'

import {
  ElDrawer,
  ElTable,
  ElTableColumn,
  ElAlert,
  ElSpace,
  ElInput,
  ElSelect,
  ElOption,
  ElSwitch,
  ElForm,
  ElFormItem,
  ElMessage,
  ElNotification,
  ElDescriptions,
  ElDescriptionsItem,
  ElTabs,
  ElTabPane,
  ElCard,
  ElEmpty
} from 'element-plus'

const show = ref(false)

const personData = ref([])

const open = (data) => {
  console.log('data', data)
  personData.value = data

  show.value = true
}

defineExpose({
  open
})
</script>

<style lang="scss" scoped>
.empty-data {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 300px;
}
</style>
