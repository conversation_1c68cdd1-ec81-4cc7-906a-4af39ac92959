<template>
  <ContentWrap title="关联账号">
    <Table
      :columns="columns"
      :data="accountCidRefList"
      :loading="loading"
      :showPagination="true"
      :pagination="{
        total: pagination.total,
        pageSizes: [10, 500, 1000]
      }"
      v-model:pageSize="pagination.pageSize"
      v-model:currentPage="pagination.currentPage"
      @update:page-size="getAccountCidRefList"
      @update:current-page="getAccountCidRefList"
      @row-dblclick="handleRowDblClick"
      @sort-change="handleSortChange"
    >
      <template #status="{ row }">
        <ElTag :type="row.status === '1' ? 'success' : 'danger'">
          {{ row.status === '1' ? '已命中' : '未命中' }}
        </ElTag>
      </template>

      <template #operate="{ row, $index }">
        <ElButton
          link
          type="primary"
          :loading="loadingRowId === getRowKey(row, $index)"
          :disabled="loadingRowId === getRowKey(row, $index)"
          @click="viewData(row, $index)"
        >
          {{ loadingRowId === getRowKey(row, $index) ? '加载中，预计需要1-5分钟' : '查看' }}
        </ElButton>
      </template>
    </Table>
    <PersonInfo ref="personInfoRef" />
  </ContentWrap>
</template>

<script setup>
import { ContentWrap } from '@/components/ContentWrap'
import { ElMessage, ElButton, ElTag } from 'element-plus'
import { onMounted, ref, onUnmounted, inject, h } from 'vue'
import { getAccountCidRefListApi, searchJiuzhangEmailApi } from '@/api/accountCidRef'
import { Table } from '@/components/Table'
import { get } from 'lodash-es'
import { useClipboard } from '@vueuse/core'
import PersonInfo from './personInfo.vue'

const props = defineProps({
  cid: {
    type: String,
    default: null
  },
  keyword: {
    type: String,
    default: null
  }
})

const personInfoRef = ref(null)
const loadingRowId = ref(null) // 记录当前加载中的行ID

const columns = ref([
  {
    field: 'account.content',
    label: '账号'
  },
  {
    field: 'num',
    label: '次数',
    sortable: true
  },
  {
    field: 'status',
    label: '状态',
    slots: {
      default: 'status'
    }
  },
  {
    label: '操作',
    field: 'operate',
    slots: {
      default: 'operate'
    }
  }
])

// 生成行的唯一标识
const getRowKey = (row, index) => {
  // 优先使用 id，如果没有则使用账号内容+索引作为唯一标识
  return row.id || `${row.account?.content || ''}_${index}`
}

const viewData = async (data, index) => {
  try {
    const rowKey = getRowKey(data, index)
    loadingRowId.value = rowKey // 设置当前加载的行标识
    const res = await searchJiuzhangEmailApi({
      email: data.account.content
    })

    personInfoRef.value.open(res.data)
  } catch (error) {
    ElMessage.error('查询失败，请稍后重试')
    console.error('查询个人信息失败:', error)
  } finally {
    loadingRowId.value = null // 清除加载状态
  }
}

const loading = ref(false)

const pagination = ref({
  pageSize: 10,
  total: 0,
  currentPage: 1
})

const sortBy = ref()

const handleSortChange = ({ column, prop, order }) => {
  if (prop === 'num' && order) {
    const orderBy = order === 'ascending' ? 'asc' : 'desc'

    pagination.value.currentPage = 1
    sortBy.value = `${prop},${orderBy}`

    getAccountCidRefList()
  }
}

const handleRowDblClick = (row, column) => {
  // 获取列的字段名
  const fieldName = column.property
  // 动态获取该字段对应的值
  const selectedValue = get(row, fieldName)
  copyContent(selectedValue)
}

// 复制内容到剪贴板
const copyContent = async (content) => {
  const { copy, copied, isSupported } = useClipboard({
    source: content,
    legacy: true
  })
  if (!isSupported) {
    ElMessage.error('复制失败')
  } else {
    await copy()
    if (copied.value) {
      ElMessage.success('复制成功')
    }
  }
}

const accountCidRefList = ref([])

const getAccountCidRefList = async () => {
  loading.value = true
  const sysInfoRes = await getAccountCidRefListApi({
    cid: props.cid,
    content: props.keyword,
    page: pagination.value.currentPage,
    size: pagination.value.pageSize,
    sortBy: sortBy.value
  })
  accountCidRefList.value = sysInfoRes?.data?.items
  pagination.value.total = sysInfoRes?.data?.total || 0
  loading.value = false
}

const eventBus = inject('eventBus')

onMounted(async () => {
  await Promise.all([getAccountCidRefList()])

  // 监听刷新事件
  eventBus.on('refreshData', (params) => {
    getAccountCidRefList()
  })
})

// 组件卸载时移除事件监听
onUnmounted(() => {
  eventBus.off('refreshData')
})
</script>
