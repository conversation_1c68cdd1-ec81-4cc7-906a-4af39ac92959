<template>
  <div v-if="isAuthorized">
    <ContentWrap class="mb-4" title="关键词搜索">
      <ElForm class="mt-4 flex flex-col" style="min-height: 100%">
        <div class="flex flex-col items-center justify-center">
          <!-- <ElRadioGroup v-model="search_type">
            <ElRadio :value="1" size="large">精确搜索（term）</ElRadio>
            <ElRadio :value="2" size="large">前缀搜索（keyword*）</ElRadio>
            <ElRadio :value="3" size="large">后缀搜索（*keyword）</ElRadio>
            <ElRadio :value="4" size="large">模糊搜索（match）</ElRadio>
            <ElRadio :value="5" size="large">模糊搜索（*keyword*）</ElRadio>
          </ElRadioGroup> -->
          <ElInput
            v-model="keyword"
            style="max-width: 600px"
            placeholder="请输入关键词进行搜索"
            class="input-with-select"
            clearable
            @keydown.enter="handleEnter"
          >
            <template #append>
              <ElButton size="large" @click="toSearch" :loading="loading">搜索一下</ElButton>
              <ElButton size="large" style="margin-left: 20px" @click="resetSearch">重置</ElButton>
            </template>
          </ElInput>
        </div>
      </ElForm>
    </ContentWrap>
    <!-- 基本信息 -->
    <BaseInfoModule :cid="cid" />
    <AccountCidRefModule :cid="cid" :keyword="keyword" class="mt-5" />
    <!-- 字段信息 -->
    <ContentWrap title="字段信息" class="mt-5">
      <ElRow :gutter="10">
        <!-- 左边 -->
        <ElCol :span="18">
          <div ref="containerRef" style="">
            <component
              v-for="(item, key) in filteredSearchTypes"
              :key="key"
              :is="components[item.flag]"
              class="mt-2"
              :id="item.flag"
              :header="item.name"
              :cid="cid"
              :keyword="keyword"
              :searchType="search_type"
              :matchModules="matchModules"
              :searchFrom="item.search_type"
            />
          </div>
        </ElCol>
        <ElDivider direction="vertical" style="height: auto" />

        <!-- 右边 -->
        <ElCol :span="5">
          <ElAffix :offset="60">
            <ElAnchor :offset="100" :container="containerRef" :duration="700" @click="handleClick">
              <div v-for="(item, key) in searchTypeRes" :key="key">
                <ElAnchorLink v-if="item.searchTypeRes !== 99" :href="`#${item.flag}`">
                  {{ item.name }}（{{ item.total }}）
                </ElAnchorLink>
              </div>
            </ElAnchor>
          </ElAffix>
        </ElCol>
      </ElRow>
    </ContentWrap>
  </div>
</template>

<script setup>
import { ContentWrap } from '@/components/ContentWrap'
import {
  ElDescriptions,
  ElDescriptionsItem,
  ElRow,
  ElCol,
  ElCard,
  ElDivider,
  ElAnchor,
  ElAnchorLink,
  ElTag,
  ElAffix,
  ElForm,
  ElRadioGroup,
  ElRadio,
  ElInput,
  ElButton,
  ElMessage
} from 'element-plus'
import { useRoute } from 'vue-router'
import { onBeforeMount, onMounted, ref, watch, provide, onUnmounted, computed } from 'vue'
import { searchTypes } from '@/constants/searchType'
import eventBus from '@/utils/eventBus'

import History from './detailComponents/history.vue'
import Password from './detailComponents/password.vue'
import Cookie from './detailComponents/cookie.vue'
import Process from './detailComponents/process.vue'
import Autofill from './detailComponents/autofill.vue'
import Software from './detailComponents/software.vue'
// import Account from './detailComponents/account.vue'
import Ftp from './detailComponents/ftp.vue'
import Screen from './detailComponents/screen.vue'
import Creditcards from './detailComponents/creditcards.vue'
import Socialfile from './detailComponents/socialfile.vue'
import Filegrabber from './detailComponents/filegrabber.vue'
import Download from './detailComponents/download.vue'

import BaseInfoModule from './baseInfo.vue'
import AccountCidRefModule from './accountCidRef.vue'

import { checkCidAuthFromPageApi, getMatchModulesApi, searchCountAPI } from '@/api/search'
import router from '@/router'
import { useTitle } from '@/hooks/web/useTitle'

// 注册组件
const components = {
  Password,
  Software,
  Cookie,
  Process,
  Autofill,
  Screen,
  History,
  Creditcards,
  Socialfile,
  Filegrabber,
  Ftp,
  Download
}

const filteredSearchTypes = computed(() => {
  return searchTypes.filter((item) => components[item.flag])
})

const { params, query } = useRoute()

const cid = params.id
const taskId = query.taskId

const containerRef = ref(null)

const keyword = ref('')
const search_type = ref(1)

const handleEnter = (e) => {
  e.preventDefault()
}

const handleClick = (e, href) => {
  e.preventDefault()
  const element = document.querySelector(href)
  if (element) {
    setTimeout(() => {
      element.scrollIntoView({
        block: 'start',
        behavior: 'smooth'
      })
    }, 20)
  }
}

const searchTypeRes = ref([])
const searchTypeLoading = ref(false)

const isAuthorized = ref(false) // 控制组件是否渲染

const searchCount = async () => {
  // searchTypeLoading.value = true
  try {
    const { data } = await searchCountAPI({
      cid: cid,
      from: 'detail',
      keyword: keyword.value
      // search_type: search_type.value
    })

    searchTypeRes.value = searchTypes.filter((item) => {
      let current = data.find((i) => i.search_type === item.search_type)
      item.total = 0
      if (current) {
        item.total = current.total
      }
      return item
    })
  } finally {
    searchTypeLoading.value = false
  }
}

const loading = ref(false)

const toSearch = async () => {
  // if (!keyword.value) {
  //   ElMessage.error('请填写您需要搜索的关键词')
  //   return
  // }

  const apiParams = {
    keyword: keyword.value
    // search_type: search_type.value
  }
  loading.value = true
  // 触发刷新事件
  eventBus.emit('refreshData', apiParams)
  await searchCount()
  loading.value = false
}

const resetSearch = () => {
  keyword.value = ''
  search_type.value = 1
}

const matchModules = ref([])

const getMatchModules = async () => {
  if (!taskId) {
    return
  }
  const { data } = await getMatchModulesApi(cid, taskId)
  matchModules.value = data
}

// 提供 eventBus 给子组件
provide('eventBus', eventBus)

onBeforeMount(async () => {
  try {
    await checkCidAuthFromPageApi({ cid })
    isAuthorized.value = true
  } catch (err) {
    router.replace('/')
  }
})

onMounted(() => {
  useTitle(`CID: ${cid}`)
  searchCount()
  getMatchModules()
})
</script>
