<template>
  <div class="search-page">
    <ElRow :gutter="10">
      <ElCol :span="5">
        <el-scrollbar :height="'calc(100vh - 70px)'">
          <el-form
            :validate-on-rule-change="false"
            :model="searchForm"
            label-position="top"
            ref="formRef"
            :rules="rules"
          >
            <div class="search-group">
              <div class="search-row">
                <el-form-item label="代理" class="keyword-input" prop="proxy">
                  <ElSelect
                    filterable
                    v-model="searchForm.proxy"
                    placeholder="请选择代理"
                    class="mt-3"
                    clearable
                  >
                    <ElOption
                      v-for="item in proxyList"
                      :key="item.id"
                      :label="item.name"
                      :value="item.name"
                    />
                  </ElSelect>
                </el-form-item>
                <el-form-item label="备注" class="keyword-input" prop="notes">
                  <ElInput
                    v-model="searchForm.notes"
                    clearable
                    placeholder="请输入备注"
                    class="mt-3"
                  />
                </el-form-item>
                <el-form-item label="关键字" class="keyword-input" prop="keyword">
                  <el-input
                    type="textarea"
                    v-model="searchForm.keyword"
                    :autosize="{ minRows: 4 }"
                    placeholder="请输入站点、用户名、密码，用|隔开，每条记录占一行"
                  />
                </el-form-item>
              </div>
            </div>
          </el-form>
          <div class="search-actions">
            <el-button
              class="mt-4"
              type="success"
              :loading="loading"
              @click="handleSearch"
              :icon="Promotion"
            >
              自动登录
            </el-button>
          </div>
        </el-scrollbar>
      </ElCol>
      <ElDivider direction="vertical" style="height: auto" />
      <ElCol :span="18">
        <el-card class="search-card" header="自动登录记录">
          <template #header>
            <div style="display: flex; justify-content: space-between; align-items: center">
              <span>自动登录记录</span>
              <el-button type="primary" plain @click="refreshTable">刷新</el-button>
            </div>
          </template>
          <TableList ref="tableListRef" />
        </el-card>
      </ElCol>
    </ElRow>
  </div>
</template>

<script setup>
import {
  ElRow,
  ElCol,
  ElCard,
  ElForm,
  ElFormItem,
  ElInput,
  ElScrollbar,
  ElDivider,
  ElButton,
  ElMessage,
  ElSelect,
  ElOption
} from 'element-plus'
import { Promotion } from '@element-plus/icons-vue'
import TableList from './components/tableList.vue'

import { onMounted, reactive, ref } from 'vue'
import { getProxyListApi, postAiAttackApi } from '@/api/aiAttack'
import { getPushConnection } from '@/utils/push'
import { useUserStore } from '@/store/modules/user'

const defaultForm = {
  keyword: null,
  proxy: null,
  notes: null
}

const loading = ref(false)
const formRef = ref()

const tableListRef = ref()

const proxyList = ref([])

const getProxyList = async () => {
  try {
    const { data } = await getProxyListApi()
    // 将对象转换为数组并格式化为select需要的格式
    proxyList.value =
      Object.entries(data.proxy).map(([id, name]) => ({
        id,
        name
      })) || []
  } catch (error) {
    console.error('获取代理列表出错:', error)
    proxyList.value = []
  }
}

const handleSearch = async () => {
  try {
    // 手动触发验证
    const valid = await formRef.value.validate()
    if (!valid) return

    loading.value = true

    // 提交
    // 检测 keyword 里面的每一行里面是否有2个|，因为每一行都是由站点|用户名|密码 组成
    const keywordList = searchForm.keyword.split('\n')
    const isValid = keywordList.every((item) => item.split('|').length === 3)
    if (!isValid) {
      ElMessage.error('关键字格式错误，请检查')
      return
    }

    const items = keywordList.map((item) => {
      const [url, username, password] = item.split('|')
      return {
        url,
        username,
        password
      }
    })

    const params = {
      proxy: searchForm.proxy,
      notes: searchForm.notes,
      items
    }

    await postAiAttackApi(params)

    resetSearch()
    refreshTable()
    ElMessage.success('操作成功')
  } catch (error) {
  } finally {
    loading.value = false
  }
}

const resetSearch = async () => {
  Object.keys(searchForm).forEach((key) => {
    searchForm[key] = defaultForm[key]
  })
  formRef.value.resetFields()
}

const refreshTable = () => {
  tableListRef.value.getList()
}

const rules = reactive({
  keyword: [{ required: true, message: '请输入关键字', trigger: 'blur' }],
  proxy: [{ required: true, message: '请选择代理', trigger: 'change' }],
  notes: [{ required: true, message: '请输入备注', trigger: 'blur' }]
})

const searchForm = reactive({ ...defaultForm })

const listenMessage = () => {
  const connection = getPushConnection()
  const userStore = useUserStore()
  // 创建监听频道
  const channel = connection.subscribe('ai-attack-' + userStore.getUserInfo.id)
  channel.on('refresh-page', function (data) {
    refreshTable()
  })
}

onMounted(() => {
  getProxyList()
  listenMessage()
})
</script>

<style lang="scss" scoped></style>
