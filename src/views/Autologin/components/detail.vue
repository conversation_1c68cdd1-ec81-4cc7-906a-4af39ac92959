<template>
  <div>
    <el-dialog v-model="dialogVisible" title="列表" :fullscreen="true" :before-close="handleClose">
      <ElForm :model="searchForm" ref="formRef">
        <ElRow :gutter="20">
          <ElCol :span="6">
            <ElFormItem label="站点" prop="url">
              <ElInput v-model="searchForm.url" clearable />
            </ElFormItem>
          </ElCol>
          <ElCol :span="6">
            <ElFormItem label="登录用户名" prop="username">
              <ElInput v-model="searchForm.username" clearable />
            </ElFormItem>
          </ElCol>
          <ElCol :span="6">
            <ElFormItem label="登录密码" prop="password">
              <ElInput v-model="searchForm.password" clearable />
            </ElFormItem>
          </ElCol>
          <ElCol :span="6">
            <ElFormItem label="失败原因" prop="failure_reason">
              <ElInput v-model="searchForm.failure_reason" clearable />
            </ElFormItem>
          </ElCol>
          <ElCol :span="6">
            <ElFormItem label="摘要" prop="extracted_information">
              <ElInput v-model="searchForm.extracted_information" clearable />
            </ElFormItem>
          </ElCol>
          <ElCol :span="6">
            <ElFormItem label="状态" prop="status">
              <ElSelect v-model="searchForm.status" multiple clearable>
                <ElOption
                  v-for="item in statusList"
                  :key="item.value"
                  :label="item.label"
                  :value="item.value"
                />
              </ElSelect>
            </ElFormItem>
          </ElCol>
          <ElCol :span="6">
            <ElFormItem label="创建时间" prop="task_created_at">
              <ElDatePicker
                v-model="searchForm.task_created_at"
                type="datetimerange"
                value-format="YYYY-MM-DD HH:mm:ss"
                clearable
              />
            </ElFormItem>
          </ElCol>
          <ElCol :span="6">
            <ElFormItem label="完成时间" prop="task_finished_at">
              <ElDatePicker
                v-model="searchForm.task_finished_at"
                type="datetimerange"
                value-format="YYYY-MM-DD HH:mm:ss"
                clearable
              />
            </ElFormItem>
          </ElCol>
        </ElRow>
        <ElRow :gutter="20">
          <ElCol :span="24">
            <ElButton type="primary" @click="getTableData">查询</ElButton>
            <ElButton @click="resetSearchForm">重置</ElButton>
          </ElCol>
        </ElRow>
      </ElForm>
      <ElTable :data="tableData" border style="width: 100%; margin-top: 30px" v-loading="loading">
        <ElTableColumn prop="url" label="站点">
          <template #default="{ row }">
            <span @click="openUrl(row.url)" @dblclick="copyContent(row.url)" class="url-link">{{
              row.url
            }}</span>
          </template>
        </ElTableColumn>
        <ElTableColumn prop="username" label="登录用户名">
          <template #default="{ row }">
            <span @dblclick="copyContent(row.username)">{{ row.username }}</span>
          </template>
        </ElTableColumn>
        <ElTableColumn prop="password" label="登录密码">
          <template #default="{ row }">
            <span @dblclick="copyContent(row.password)">{{ row.password }}</span>
          </template>
        </ElTableColumn>
        <!-- <ElTableColumn prop="workflow_id" label="Workflow ID" /> -->
        <ElTableColumn prop="status_code" label="Http状态码" />
        <ElTableColumn prop="status" label="状态">
          <template #default="{ row }">
            <ElTag v-if="row.status" :type="filterStatus(row.status, true)">{{
              filterStatus(row.status)
            }}</ElTag>
          </template>
        </ElTableColumn>
        <ElTableColumn width="200" prop="login_result" label="登录状态">
          <template #default="{ row }">
            <ElTag v-if="row.status" :type="filterLoginStatus(row.login_result, true)">{{
              filterLoginStatus(row.login_result)
            }}</ElTag>
          </template>
        </ElTableColumn>
        <ElTableColumn prop="outputs.error_text" label="失败原因">
          <template #default="{ row }">
            <ElTooltip v-if="row.error_text?.length > 50" :content="row.error_text" placement="top">
              <span @dblclick="copyContent(row.error_text)">
                {{ row.error_text.slice(0, 50) }}...
              </span>
            </ElTooltip>
            <span v-else @dblclick="copyContent(row.error_text)">
              {{ row.error_text || '' }}
            </span>
          </template>
        </ElTableColumn>
        <ElTableColumn prop="outputs.description" label="摘要">
          <template #default="{ row }">
            <ElTooltip
              v-if="row.description?.length > 50"
              :content="row.description"
              placement="top"
            >
              <span @dblclick="copyContent(row.description)">
                {{ row.description.slice(0, 50) }}...
              </span>
            </ElTooltip>
            <span v-else @dblclick="copyContent(row.description)">
              {{ row.description || '' }}
            </span>
          </template>
        </ElTableColumn>
        <ElTableColumn prop="screenshots" label="截屏">
          <template #default="{ row }">
            <ElImage
              v-for="(item, key) in row.screenshots"
              style="height: 100px"
              :key="key"
              :src="`${item}`"
              :preview-src-list="[`${item}`]"
              fit="cover"
            />
          </template>
        </ElTableColumn>
        <ElTableColumn prop="task_created_at" label="创建时间" />
        <ElTableColumn prop="task_finished_at" label="完成时间" />
      </ElTable>
    </el-dialog>
  </div>
</template>

<script setup>
import {
  ElCard,
  ElTable,
  ElTableColumn,
  ElButton,
  ElImage,
  ElTag,
  ElDialog,
  ElTooltip,
  ElMessage,
  ElForm,
  ElFormItem,
  ElInput,
  ElRow,
  ElCol,
  ElSelect,
  ElOption,
  ElDatePicker
} from 'element-plus'
import { ref, onMounted, reactive } from 'vue'
import dayjs from 'dayjs'
import utc from 'dayjs/plugin/utc'
import timezone from 'dayjs/plugin/timezone'
import { getAiAttackDetailApi } from '@/api/aiAttack'
import { useClipboard } from '@vueuse/core'

const dialogVisible = ref(false)

// 配置 Day.js 插件
dayjs.extend(utc)
dayjs.extend(timezone)

const searchForm = reactive({
  url: null,
  username: null,
  password: null,
  failure_reason: null,
  extracted_information: null,
  status: [],
  task_created_at: [],
  task_finished_at: []
})

const formRef = ref()

const resetSearchForm = () => {
  formRef.value.resetFields()
  getTableData()
}

const tableData = ref([])
const aiAttackId = ref('')
const loading = ref(false)
const formatDate = (dateString) => {
  return dayjs.utc(dateString).local().format('YYYY-MM-DD HH:mm:ss')
}

const copyContent = async (content) => {
  const { copy, copied, isSupported } = useClipboard({
    source: content,
    legacy: true
  })
  if (!isSupported) {
    ElMessage.error('复制失败')
  } else {
    await copy()
    if (copied.value) {
      ElMessage.success('复制成功')
    }
  }
}

const statusList = ref([
  {
    label: '已完成',
    value: 'completed'
  },
  {
    label: '已终止',
    value: 'terminated'
  },
  {
    label: '失败',
    value: 'failed'
  },
  {
    label: '运行中',
    value: 'running'
  },
  {
    label: '等待执行',
    value: 'queued'
  }
])

const filterStatus = (status, isColor = false) => {
  const statusMap = {
    completed: isColor ? 'success' : '已完成',
    terminated: isColor ? 'danger' : '已终止',
    failed: isColor ? 'danger' : '失败',
    running: isColor ? 'warning' : '运行中',
    queued: isColor ? 'info' : '等待执行'
  }
  return statusMap[status] || status
}

const filterLoginStatus = (status, isColor = false) => {
  const statusMap = {
    login_success: isColor ? 'success' : '登陆成功，账号密码正确',
    login_failed: isColor ? 'danger' : '登陆失败，账号或密码不正确',
    captcha_blocked: isColor ? 'danger' : '无法绕过验证码',
    two_factor_required: isColor ? 'danger' : '需要两步验证',
    page_not_found: isColor ? 'danger' : '无法访问相应页面',
    input_not_found: isColor ? 'danger' : '无法找到登录框',
    login_error: isColor ? 'warning' : '其他无法登录的情况'
  }
  return statusMap[status] || status
}

const handleClose = () => {
  dialogVisible.value = false
}

const getTableData = async () => {
  loading.value = true
  const { data: res } = await getAiAttackDetailApi(aiAttackId.value, searchForm)
  tableData.value = res
  loading.value = false
}

const open = (id) => {
  aiAttackId.value = id
  dialogVisible.value = true
  getTableData()
}

const openUrl = (url) => {
  if (!url) return
  // 验证URL格式
  try {
    new URL(url)
    window.open(url, '_blank')
  } catch (e) {
    ElMessage.error('无效的URL地址')
  }
}

defineExpose({
  open
})
</script>

<style lang="scss">
/* 确保这些样式是全局的（不使用 scoped） */
.el-table__body-wrapper {
  // 调整表格 hover 的 z-index
  .el-table__row:hover > td {
    z-index: auto !important;
  }
}

// 图片预览相关样式
.el-image-viewer__wrapper {
  position: fixed !important; // 确保是相对于视口定位
  z-index: 3000 !important; // 使用更高的 z-index
}

.el-image-viewer__mask {
  position: fixed !important;
  z-index: 2999 !important;
}

.el-image-viewer__btn {
  z-index: 3001 !important;
}

.el-image-viewer__img {
  z-index: 3000 !important;
}

.url-link {
  color: #409eff;
  cursor: pointer;
  &:hover {
    text-decoration: underline;
  }
}
</style>
