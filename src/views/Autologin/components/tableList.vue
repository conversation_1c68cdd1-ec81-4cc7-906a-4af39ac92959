<template>
  <div>
    <Table
      v-model:current-page="currentPage"
      v-model:page-size="pageSize"
      :columns="allSchemas.tableColumns"
      :data="dataList"
      :loading="loading"
      @register="tableRegister"
      :pagination="{
        total
      }"
    />
    <DetailModule ref="detailRef" />
    <FormModule ref="formModuleRef" @success="getList" />
  </div>
</template>

<script setup lang="tsx">
import { Table } from '@/components/Table'
import { useTable } from '@/hooks/web/useTable'
import { CrudSchema, useCrudSchemas } from '@/hooks/web/useCrudSchemas'
import { reactive, ref, unref } from 'vue'
import { BaseButton } from '@/components/Button'
import DetailModule from './detail.vue'
import { getAiAttackListApi } from '@/api/aiAttack'
import { ElTag } from 'element-plus'
import { filterAutoLoginStatus } from '@/utils/filter'
import FormModule from './form.vue'
import dayjs from 'dayjs'
import utc from 'dayjs/plugin/utc'
import timezone from 'dayjs/plugin/timezone'
// 配置 Day.js 插件
dayjs.extend(utc)
dayjs.extend(timezone)
const dialogVisible = ref(false)

const taskControlId = ref()
const formModuleRef = ref()

const { tableRegister, tableState, tableMethods } = useTable({
  fetchDataApi: async () => {
    let items = []
    let total = 0
    const { pageSize, currentPage } = tableState

    const res = await getAiAttackListApi({
      page: unref(currentPage),
      size: unref(pageSize)
    })
    const { data } = res
    items = data.items
    total = data.total

    return {
      list: items,
      total: total
    }
  }
})

const { total, loading, dataList, pageSize, currentPage } = tableState
const { getList } = tableMethods

const crudSchemas = reactive<CrudSchema[]>([
  {
    field: 'selection',
    search: {
      hidden: true
    },
    form: {
      hidden: true
    },
    detail: {
      hidden: true
    },
    table: {
      type: 'selection'
    }
  },
  {
    field: 'index',
    label: '序号',
    type: 'index',
    search: {
      hidden: true
    }
  },
  {
    field: 'notes',
    label: '备注',
    minWidth: '160',
    search: {
      hidden: true
    }
  },
  {
    field: 'proxy_name',
    label: '代理',
    minWidth: '160',
    search: {
      hidden: true
    }
  },

  {
    field: 'start_at',
    label: '开始时间',
    minWidth: '160',
    search: {
      hidden: true
    }
  },
  {
    field: 'end_at',
    label: '结束时间',
    minWidth: '160',
    search: {
      hidden: true
    }
  },
  {
    field: 'items_count',
    label: '密码数量',
    minWidth: '100',
    search: {
      hidden: true
    }
  },
  {
    field: 'status',
    label: '状态',
    minWidth: '100',
    search: {
      hidden: true
    },
    slots: {
      default: (data: any) => {
        return (
          <>
            <ElTag type={filterAutoLoginStatus(data.row.status, true)}>
              {filterAutoLoginStatus(data.row.status)}
            </ElTag>
          </>
        )
      }
    }
  },
  {
    field: 'user.username',
    label: '操作人',
    minWidth: '120',
    search: {
      hidden: true
    }
  },

  {
    field: 'action',
    label: '操作',
    search: {
      hidden: true
    },
    fixed: 'right',
    minWidth: '150',
    slots: {
      default: (data: any) => {
        const row = data.row
        return (
          <>
            <BaseButton type="primary" size="small" onClick={() => detail(row)}>
              详情
            </BaseButton>
            <BaseButton type="success" size="small" onClick={() => edit(row)}>
              编辑
            </BaseButton>
          </>
        )
      }
    }
  }
])

const { allSchemas } = useCrudSchemas(crudSchemas)

const detailRef = ref()
const detail = (row) => {
  detailRef.value.open(row.id)
}

const edit = (row) => {
  formModuleRef.value.open(row)
}

const open = (id) => {
  taskControlId.value = id
  getList()
  dialogVisible.value = true
}

const formatDate = (dateString) => {
  return dayjs.utc(dateString).local().format('YYYY-MM-DD HH:mm:ss')
}

defineExpose({
  open,
  getList
})
</script>

<style lang="scss" scoped></style>
