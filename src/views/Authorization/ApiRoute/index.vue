<script setup lang="tsx">
import { reactive, ref, unref } from 'vue'
import {
  getApiRouteListApi,
  createApiRouteApi,
  updateApiRouteApi,
  deleteApiRouteByIdApi
} from '@/api/apiRoute'
import { useTable } from '@/hooks/web/useTable'
import { Table } from '@/components/Table'
import { ElMessage, ElTag } from 'element-plus'
import { Search } from '@/components/Search'
import { ContentWrap } from '@/components/ContentWrap'
import Write from './components/Write.vue'
import { Dialog } from '@/components/Dialog'
import { CrudSchema, useCrudSchemas } from '@/hooks/web/useCrudSchemas'
import { BaseButton } from '@/components/Button'
import { hasPermi } from '@/components/Permission'

const delLoading = ref(false)
const ids = ref<string[]>([])

const { tableRegister, tableState, tableMethods } = useTable({
  fetchDataApi: async () => {
    const { pageSize, currentPage } = tableState
    const res = await getApiRouteListApi({
      page: unref(currentPage),
      size: unref(pageSize),
      ...unref(searchParams)
    })
    return {
      list: res.data.items || [],
      total: res.data.total
    }
  },
  fetchDelApi: async () => {
    const res = await deleteApiRouteByIdApi(unref(ids))
    return !!res
  }
})

const { total, loading, dataList, pageSize, currentPage } = tableState
const { getList, getElTableExpose, delList } = tableMethods

const crudSchemas = reactive<CrudSchema[]>([
  {
    field: 'selection',
    search: {
      hidden: true
    },
    form: {
      hidden: true
    },
    detail: {
      hidden: true
    },
    table: {
      type: 'selection'
    }
  },
  {
    field: 'index',
    label: '序号',
    type: 'index',
    search: {
      hidden: true
    }
  },
  {
    field: 'uri',
    label: 'API地址'
  },
  {
    field: 'method',
    label: '请求方式',
    search: {
      hidden: true
    }
  },
  {
    field: 'note',
    label: '描述',
    search: {
      hidden: true
    }
  },
  {
    field: 'created_at',
    label: '创建时间',
    search: {
      hidden: true
    }
  },
  {
    field: 'action',
    label: '操作',
    width: 240,
    search: {
      hidden: true
    },
    slots: {
      default: (data: any) => {
        const row = data.row
        return (
          <>
            {hasPermi('/apiRoute/update') && (
              <BaseButton type="primary" size="small" onClick={() => action(row, 'edit')}>
                编辑
              </BaseButton>
            )}

            {/* <BaseButton type="success" onClick={() => action(row, 'detail')}>
              详情
            </BaseButton> */}

            {hasPermi('/apiRoute/delete') && (
              <BaseButton type="danger" size="small" onClick={() => delData(row)}>
                删除
              </BaseButton>
            )}
          </>
        )
      }
    }
  }
])

const { allSchemas } = useCrudSchemas(crudSchemas)

const searchParams = ref({})
const setSearchParams = (data: any) => {
  searchParams.value = data
  getList()
}

const dialogVisible = ref(false)
const dialogTitle = ref('')

const currentRow = ref()
const actionType = ref('')

const writeRef = ref<ComponentRef<typeof Write>>()

const saveLoading = ref(false)

const action = (row: any, type: string) => {
  dialogTitle.value = type === 'edit' ? '编辑' : '详情'
  actionType.value = type
  currentRow.value = row
  dialogVisible.value = true
}

const AddAction = () => {
  dialogTitle.value = '新增'
  currentRow.value = undefined
  dialogVisible.value = true
  actionType.value = ''
}

const delData = async (row: any = null) => {
  const elTableExpose = await getElTableExpose()
  ids.value = row ? [row.id] : elTableExpose?.getSelectionRows().map((v) => v.id) || []
  if (ids.value.length <= 0) {
    ElMessage.error({
      message: '请至少选择一条记录'
    })
    return
  }
  delLoading.value = true

  await delList(unref(ids).length).finally(() => {
    delLoading.value = false
  })
}

const save = async () => {
  const write = unref(writeRef)
  const formData = await write?.submit()
  if (formData) {
    saveLoading.value = true

    if (formData.id) {
      await updateApiRouteApi(formData.id, formData)
    } else {
      await createApiRouteApi(formData)
    }
    saveLoading.value = false
    dialogVisible.value = false
    ElMessage.success('操作成功')
    getList()
  }
}
</script>

<template>
  <ContentWrap>
    <!-- <Search :schema="allSchemas.searchSchema" @reset="setSearchParams" @search="setSearchParams" /> -->
    <div class="mb-10px">
      <BaseButton type="primary" @click="AddAction" v-hasPermi="`/apiRoute/add`">新增</BaseButton>
      <BaseButton
        :loading="delLoading"
        type="danger"
        v-hasPermi="`/apiRoute/delete`"
        @click="delData()"
      >
        删除
      </BaseButton>
    </div>
    <Table
      v-model:current-page="currentPage"
      v-model:page-size="pageSize"
      :columns="allSchemas.tableColumns"
      :data="dataList"
      :loading="loading"
      @register="tableRegister"
      :pagination="{
        total
      }"
    />
  </ContentWrap>

  <Dialog v-model="dialogVisible" :title="dialogTitle">
    <Write ref="writeRef" :current-row="currentRow" />

    <template #footer>
      <BaseButton
        v-if="actionType !== 'detail'"
        type="primary"
        :loading="saveLoading"
        @click="save"
      >
        保存
      </BaseButton>
      <BaseButton @click="dialogVisible = false">关闭</BaseButton>
    </template>
  </Dialog>
</template>
