<script setup lang="tsx">
import { reactive, ref, unref } from 'vue'
import { getMenuListApi, createMenuApi, updateMenuApi, deleteMenu<PERSON><PERSON> } from '@/api/menu'
import { useTable } from '@/hooks/web/useTable'
import { Table, TableColumn } from '@/components/Table'
import { ElMessage, ElMessageBox, ElTag } from 'element-plus'
import { Icon } from '@/components/Icon'
import { Search } from '@/components/Search'
import { FormSchema } from '@/components/Form'
import { ContentWrap } from '@/components/ContentWrap'
import Write from './components/Write.vue'
import Detail from './components/Detail.vue'
import { Dialog } from '@/components/Dialog'
import { BaseButton } from '@/components/Button'
import { hasPermi } from '@/components/Permission'

import ApiRouteModule from './components/apiRoute.vue'

const searchParams = ref({})

const { tableRegister, tableState, tableMethods } = useTable({
  fetchDataApi: async () => {
    const res = await getMenuListApi(searchParams.value)
    return {
      list: res.data.list || []
    }
  }
})

const { dataList, loading } = tableState
const { getList } = tableMethods

const tableColumns = reactive<TableColumn[]>([
  {
    field: 'index',
    label: '序号',
    type: 'index'
  },
  {
    field: 'meta.title',
    label: '菜单名称',
    minWidth: '200',
    slots: {
      default: (data: any) => {
        const title = data.row.meta.title
        return <>{title}</>
      }
    }
  },
  {
    field: 'meta.icon',
    label: '图标',
    slots: {
      default: (data: any) => {
        const icon = data.row.meta.icon
        if (icon) {
          return (
            <>
              <Icon icon={icon} />
            </>
          )
        } else {
          return null
        }
      }
    }
  },
  {
    field: 'type',
    label: '类型',
    slots: {
      default: (data: any) => {
        const colorMap = {
          0: 'info',
          1: 'success',
          2: 'warning'
        }
        const typeMap = {
          0: '目录',
          1: '菜单',
          2: '按钮'
        }
        return (
          <>
            <ElTag type={colorMap[data.row.type]}>{typeMap[data.row.type]}</ElTag>
          </>
        )
      }
    }
  },
  // {
  //   field: 'meta.permission',
  //   label: t('menu.permission'),
  //   slots: {
  //     default: (data: any) => {
  //       const permission = data.row.meta.permission
  //       return permission ? <>{permission.join(', ')}</> : null
  //     }
  //   }
  // },
  {
    field: 'component',
    label: '组件',
    slots: {
      default: (data: any) => {
        const component = data.row.component
        if (data.row.type === 2) {
          return null
        }
        return <>{component === '#' ? '顶级目录' : component === '##' ? '子目录' : component}</>
      }
    }
  },
  {
    field: 'path',
    label: '路径'
  },
  {
    field: 'permission_code',
    label: '权限标识',
    minWidth: 150
  },
  {
    field: 'sort',
    label: '排序'
  },
  {
    field: 'status',
    label: '状态',
    slots: {
      default: (data: any) => {
        return (
          <>
            <ElTag type={data.row.status === 0 ? 'danger' : 'success'}>
              {data.row.status === 1 ? '启用' : '禁用'}
            </ElTag>
          </>
        )
      }
    }
  },
  {
    field: 'action',
    label: '操作',
    width: 350,
    slots: {
      default: (data: any) => {
        const row = data.row
        return (
          <>
            {hasPermi('/menu/api') && (
              <BaseButton type="warning" size="small" onClick={() => apiAction(row)}>
                API权限
              </BaseButton>
            )}

            {hasPermi('/menu/update') && (
              <BaseButton type="primary" size="small" onClick={() => action(row, 'edit')}>
                编辑
              </BaseButton>
            )}

            <BaseButton type="success" size="small" onClick={() => action(row, 'detail')}>
              详情
            </BaseButton>
            {hasPermi('/menu/delete') && (
              <BaseButton type="danger" size="small" onClick={() => delData(row)}>
                删除
              </BaseButton>
            )}
          </>
        )
      }
    }
  }
])

const searchSchema = reactive<FormSchema[]>([
  {
    field: 'title',
    label: '菜单名称',
    component: 'Input'
  }
])

const setSearchParams = (data: any) => {
  searchParams.value = data
  getList()
}

const dialogVisible = ref(false)
const dialogTitle = ref('')

const apiRouteVisible = ref(false)

const currentRow = ref()
const actionType = ref('')

const writeRef = ref<ComponentRef<typeof Write>>()

const saveLoading = ref(false)

const apiAction = (row: any) => {
  currentRow.value = row
  apiRouteVisible.value = true
}

const action = (row: any, type: string) => {
  dialogTitle.value = type === 'edit' ? '编辑' : '详情'
  actionType.value = type
  currentRow.value = row
  dialogVisible.value = true
}

const AddAction = () => {
  dialogTitle.value = '新增'
  currentRow.value = undefined
  dialogVisible.value = true
  actionType.value = ''
}

const delData = async (row: any = null) => {
  ElMessageBox.confirm('是否删除此条记录', '提示', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning'
  })
    .then(async () => {
      const id = row.id
      await deleteMenuApi(id).finally(() => {})

      getList()
    })
    .catch(() => {})
}

const save = async () => {
  const write = unref(writeRef)
  const formData = await write?.submit()
  console.log(formData)
  if (formData) {
    saveLoading.value = true

    try {
      if (formData.id) {
        await updateMenuApi(formData.id, formData)
      } else {
        await createMenuApi(formData)
      }
    } finally {
      saveLoading.value = false
    }

    dialogVisible.value = false
    ElMessage.success('操作成功')
    getList()
  }
}
</script>

<template>
  <ContentWrap>
    <Search :schema="searchSchema" @reset="setSearchParams" @search="setSearchParams" />
    <div class="mb-10px">
      <BaseButton type="primary" @click="AddAction" v-hasPermi="`/menu/add`">新增</BaseButton>
    </div>
    <Table
      :columns="tableColumns"
      default-expand-all
      node-key="id"
      :data="dataList"
      :loading="loading"
      @register="tableRegister"
      :show-pagination="false"
    />
  </ContentWrap>

  <Dialog v-model="dialogVisible" :title="dialogTitle">
    <Write v-if="actionType !== 'detail'" ref="writeRef" :current-row="currentRow" />

    <Detail v-if="actionType === 'detail'" :current-row="currentRow" />

    <template #footer>
      <BaseButton
        v-if="actionType !== 'detail'"
        type="primary"
        :loading="saveLoading"
        @click="save"
      >
        保存
      </BaseButton>
      <BaseButton @click="dialogVisible = false">关闭</BaseButton>
    </template>
  </Dialog>

  <ApiRouteModule
    v-if="apiRouteVisible"
    :visible="apiRouteVisible"
    :item="currentRow"
    @update:visible="apiRouteVisible = $event"
  />
</template>
