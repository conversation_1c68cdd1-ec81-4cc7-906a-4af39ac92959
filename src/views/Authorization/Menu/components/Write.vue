<script setup lang="tsx">
import { Form, FormSchema } from '@/components/Form'
import { useForm } from '@/hooks/web/useForm'
import { PropType, reactive, watch, ref, unref } from 'vue'
import { useValidator } from '@/hooks/web/useValidator'
import { getMenuListApi } from '@/api/menu'
import { ElButton, ElInput, ElPopconfirm, ElTable, ElTableColumn, ElTag } from 'element-plus'
import AddButtonPermission from './AddButtonPermission.vue'
import { BaseButton } from '@/components/Button'
import { cloneDeep } from 'lodash-es'

const { required } = useValidator()

const props = defineProps({
  currentRow: {
    type: Object as PropType<any>,
    default: () => null
  }
})

// const handleClose = async (tag: any) => {
//   const formData = await getFormData()
//   // 删除对应的权限
//   setValues({
//     permission_list: formData?.permission_list?.filter((v: any) => v.value !== tag.value)
//   })
// }

// const handleEdit = async (row: any) => {
//   // 深拷贝当前行数据到编辑行
//   permissionEditingRow.value = { ...row }
// }

// const handleSave = async () => {
//   const formData = await getFormData()
//   const index = formData?.permission_list?.findIndex((x) => x.id === permissionEditingRow.value.id)
//   if (index !== -1) {
//     formData.permission_list[index] = { ...permissionEditingRow.value }
//     permissionEditingRow.value = null // 重置编辑状态
//   }
// }

const showDrawer = ref(false)
// 存储正在编辑的行的数据
// const permissionEditingRow = ref<any>(null)

const formSchema = reactive<FormSchema[]>([
  {
    field: 'type',
    label: '菜单类型',
    component: 'RadioButton',
    value: 0,
    colProps: {
      span: 24
    },
    componentProps: {
      options: [
        {
          label: '目录',
          value: 0
        },
        {
          label: '菜单',
          value: 1
        },
        {
          label: '按钮',
          value: 2
        }
      ],
      on: {
        change: async (val: number) => {
          const formData = await getFormData()
          if (val === 2) {
            setSchema([
              {
                field: 'component',
                path: 'hidden',
                value: true
              },
              {
                field: 'name',
                path: 'hidden',
                value: true
              },
              {
                field: 'meta.icon',
                path: 'hidden',
                value: true
              },
              {
                field: 'path',
                path: 'hidden',
                value: true
              },
              {
                field: 'meta.activeMenu',
                path: 'hidden',
                value: true
              },
              {
                field: 'meta.hidden',
                path: 'hidden',
                value: true
              },
              {
                field: 'meta.no_cache',
                path: 'hidden',
                value: true
              }
            ])
          } else {
            setSchema([
              {
                field: 'component',
                path: 'hidden',
                value: false
              },
              {
                field: 'name',
                path: 'hidden',
                value: false
              },
              {
                field: 'meta.icon',
                path: 'hidden',
                value: false
              },
              {
                field: 'path',
                path: 'hidden',
                value: false
              },
              {
                field: 'meta.activeMenu',
                path: 'hidden',
                value: false
              },
              {
                field: 'meta.hidden',
                path: 'hidden',
                value: false
              },
              {
                field: 'meta.no_cache',
                path: 'hidden',
                value: false
              }
            ])
          }
          if (val === 1) {
            setSchema([
              {
                field: 'component',
                path: 'componentProps.disabled',
                value: false
              }
            ])
            setValues({
              component: unref(cacheComponent)
            })
          } else {
            setSchema([
              {
                field: 'component',
                path: 'componentProps.disabled',
                value: true
              }
            ])

            if (formData.parent_id === void 0) {
              setValues({
                component: '#'
              })
            } else {
              setValues({
                component: '##'
              })
            }
          }
        }
      }
    }
  },
  {
    field: 'parent_id',
    label: '父级菜单',
    component: 'TreeSelect',
    componentProps: {
      nodeKey: 'id',
      props: {
        label: 'title',
        value: 'id',
        children: 'children'
      },
      highlightCurrent: true,
      expandOnClickNode: false,
      checkStrictly: true,
      checkOnClickNode: true,
      clearable: true,
      on: {
        change: async (val: number) => {
          const formData = await getFormData()
          if (val && formData.type === 0) {
            setValues({
              component: '##'
            })
          } else if (!val && formData.type === 0) {
            setValues({
              component: '#'
            })
          } else if (formData.type === 1) {
            setValues({
              component: unref(cacheComponent) ?? ''
            })
          }
        }
      }
    },
    optionApi: async () => {
      const res = await getMenuListApi()
      return res.data.list || []
    }
  },
  {
    field: 'meta.title',
    label: '菜单名称',
    component: 'Input'
  },
  {
    field: 'component',
    label: '组件',
    component: 'Input',
    value: '#',
    componentProps: {
      disabled: true,
      placeholder: '#为顶级目录，##为子目录',
      on: {
        change: (val: string) => {
          cacheComponent.value = val
        }
      }
    }
  },
  {
    field: 'name',
    label: '组件名称',
    component: 'Input'
  },
  {
    field: 'meta.icon',
    label: '图标',
    component: 'Input'
  },
  {
    field: 'path',
    label: '路径',
    component: 'Input'
  },
  {
    field: 'meta.activeMenu',
    label: '高亮菜单',
    component: 'Input'
  },
  {
    field: 'status',
    label: '状态',
    component: 'Select',
    value: 1,
    componentProps: {
      options: [
        {
          label: '启用',
          value: 1
        },
        {
          label: '禁用',
          value: 0
        }
      ]
    }
  },
  {
    field: 'meta.hidden',
    label: '是否隐藏',
    component: 'Switch'
  },
  {
    field: 'meta.no_cache',
    label: '是否清除缓存',
    component: 'Switch'
  },
  {
    field: 'permission_code',
    label: '权限标识',
    component: 'Input',
    colProps: {
      span: 24
    }
  },
  {
    field: 'sort',
    label: '排序',
    component: 'InputNumber'
  }
])

const rules = reactive({
  component: [required()],
  path: [required()],
  'meta.title': [required()]
})

const { formRegister, formMethods } = useForm()
const { setValues, getFormData, getElFormExpose, setSchema } = formMethods

const submit = async () => {
  const elForm = await getElFormExpose()
  const valid = await elForm?.validate().catch((err) => {
    console.log(err)
  })
  if (valid) {
    const formData = await getFormData()
    return formData
  }
}

const cacheComponent = ref('')

watch(
  () => props.currentRow,
  (value) => {
    if (!value) return
    const currentRow = cloneDeep(value)
    cacheComponent.value = currentRow.type === 1 ? currentRow.component : ''
    if (currentRow.parent_id === 0) {
      setSchema([
        {
          field: 'component',
          path: 'componentProps.disabled',
          value: true
        }
      ])
    } else {
      setSchema([
        {
          field: 'component',
          path: 'componentProps.disabled',
          value: false
        }
      ])
    }
    if (currentRow.type === 1) {
      setSchema([
        {
          field: 'component',
          path: 'componentProps.disabled',
          value: false
        }
      ])
    } else {
      setSchema([
        {
          field: 'component',
          path: 'componentProps.disabled',
          value: true
        }
      ])
    }
    setValues(currentRow)
  },
  {
    deep: true,
    immediate: true
  }
)

defineExpose({
  submit
})

const confirm = async (data: any) => {
  const formData = await getFormData()
  setValues({
    permission_list: [...(formData?.permission_list || []), data]
  })
}
</script>

<template>
  <Form :rules="rules" @register="formRegister" :schema="formSchema" />
  <AddButtonPermission v-model="showDrawer" @confirm="confirm" />
</template>
