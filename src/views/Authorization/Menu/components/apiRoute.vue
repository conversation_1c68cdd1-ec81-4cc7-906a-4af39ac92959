<template>
  <Dialog
    :model-value="visible"
    @update:visible="$emit('update:visible', $event)"
    @closed="$emit('update:visible', false)"
    title="设置API权限"
  >
    <ElTable ref="tableRef" :data="tableData" border style="width: 100%">
      <ElTableColumn type="selection" width="55" />
      <ElTableColumn prop="uri" label="API地址" />
      <ElTableColumn prop="method" label="请求方式" />
      <ElTableColumn prop="note" label="描述" />
    </ElTable>

    <template #footer>
      <BaseButton type="primary" :loading="saveLoading" @click="saveApiRoute"> 保存 </BaseButton>
      <BaseButton @click="$emit('update:visible', false)">关闭</BaseButton>
    </template>
  </Dialog>
</template>

<script setup>
import { getApiRouteListApi } from '@/api/apiRoute'
import { ElMessage, ElTable, ElTableColumn } from 'element-plus'
import { onMounted, ref } from 'vue'
import { Dialog } from '@/components/Dialog'
import { getApiRouteIdsApi, setMenuApiRouteApi } from '@/api/menu'

const props = defineProps({
  visible: {
    type: Boolean,
    default: false
  },
  item: {
    type: Object,
    default: () => ({})
  }
})

const emit = defineEmits(['update:visible'])

const tableData = ref([])
const saveLoading = ref(false)
const tableRef = ref(null)
const defaultApiRouteIds = ref([])

const getTableData = async () => {
  const res = await getApiRouteListApi({ paging: 0 })
  tableData.value = res.data
  await tableRef.value.clearSelection()
  tableData.value.map((item) => {
    if (defaultApiRouteIds.value.includes(item.id)) {
      tableRef.value.toggleRowSelection(item, true)
    }
  })
}

const getApiRouteIds = async () => {
  const res = await getApiRouteIdsApi(props.item.id)
  const { data } = res || []
  defaultApiRouteIds.value = data || []
}

onMounted(async () => {
  await getApiRouteIds()
  await getTableData()
})

const saveApiRoute = async () => {
  saveLoading.value = true
  const ids = tableRef.value.getSelectionRows().map((item) => item.id)
  try {
    await setMenuApiRouteApi(props.item.id, {
      api_route_ids: ids
    })

    ElMessage.success('操作成功')
    emit('update:visible', false)
  } finally {
    saveLoading.value = false
  }
}
</script>

<style lang="scss" scoped></style>
