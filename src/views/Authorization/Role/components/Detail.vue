<script setup lang="tsx">
import { PropType, ref, unref, nextTick, computed } from 'vue'
import { Descriptions, DescriptionsSchema } from '@/components/Descriptions'
import { ElTag, ElTree } from 'element-plus'
import { findIndex } from '@/utils'
import { getMenuListApi } from '@/api/menu'

const props = defineProps({
  currentRow: {
    type: Object as PropType<any>,
    default: () => undefined
  }
})

const renderTag = (enable?: boolean) => {
  return <ElTag type={!enable ? 'danger' : 'success'}>{enable ? '启用' : '禁用'}</ElTag>
}

const treeRef = ref<typeof ElTree>()

const treeData = ref<any[]>([])
const getMenuList = async () => {
  const res = await getMenuListApi()
  if (res) {
    treeData.value = res.data.list
    await nextTick()
  }
}
getMenuList()

const filteredTreeData = computed(() => {
  if (props.currentRow?.menus && treeData.value.length) {
    return filterTreeData(treeData.value, props.currentRow.menus)
  }
  return []
})

const filterTreeData = (data, menus) => {
  return data.filter((item) => {
    if (menus.includes(item.id)) {
      if (item.children && item.children.length > 0) {
        item.children = filterTreeData(item.children, menus)
      }
      return true
    }
    return false
  })
}

const detailSchema = ref<DescriptionsSchema[]>([
  {
    field: 'name',
    label: '角色名称'
  },
  {
    field: 'status',
    label: '状态',
    slots: {
      default: (data: any) => {
        return renderTag(data.status)
      }
    }
  },
  {
    field: 'remark',
    label: '备注',
    span: 24
  },
  {
    field: 'menus',
    label: '菜单权限',
    span: 24,
    slots: {
      default: ({ menus }) => {
        return (
          <>
            <div class="flex w-full">
              <div class="flex-1">
                <ElTree
                  ref={treeRef}
                  node-key="id"
                  props={{ children: 'children', label: 'title' }}
                  highlight-current
                  expand-on-click-node={false}
                  data={filteredTreeData.value}
                  default-expanded-keys={menus}
                >
                  {{
                    default: (data) => {
                      return <span>{data?.data?.meta?.title || data?.data?.title}</span>
                    }
                  }}
                </ElTree>
              </div>
            </div>
          </>
        )
      }
    }
  }
])
</script>

<template>
  <Descriptions :schema="detailSchema" :data="currentRow || {}" />
</template>
