<script setup lang="tsx">
import { Form, FormSchema } from '@/components/Form'
import { useForm } from '@/hooks/web/useForm'
import { PropType, reactive, watch, ref, unref } from 'vue'
import { useValidator } from '@/hooks/web/useValidator'
import { ElTree } from 'element-plus'
import { getMenuListApi } from '@/api/menu'

const { required } = useValidator()

const props = defineProps({
  currentRow: {
    type: Object as PropType<any>,
    default: () => null
  }
})

const treeRef = ref<typeof ElTree>()

const formSchema = ref<FormSchema[]>([
  {
    field: 'name',
    label: '角色名称',
    component: 'Input'
  },
  {
    field: 'status',
    label: '状态',
    component: 'Select',
    componentProps: {
      options: [
        {
          label: '禁用',
          value: 0
        },
        {
          label: '启用',
          value: 1
        }
      ]
    }
  },
  {
    field: 'level',
    label: '等级',
    component: 'Select',
    componentProps: {
      options: [
        {
          label: '等级1',
          value: 1
        },
        {
          label: '等级2',
          value: 2
        },
        {
          label: '等级3',
          value: 3
        },
        {
          label: '等级4',
          value: 4
        },
        {
          label: '等级5',
          value: 5
        }
      ]
    }
  },
  {
    field: 'remark',
    label: '备注',
    component: 'Input',
    componentProps: {
      type: 'textarea',
      rows: 2
    }
  },
  {
    field: 'menus',
    label: '菜单权限',
    colProps: {
      span: 24
    },
    formItemProps: {
      slots: {
        default: (data) => {
          return (
            <>
              <div class="w-full">
                <ElTree
                  ref={treeRef}
                  show-checkbox
                  node-key="id"
                  highlight-current
                  expand-on-click-node={false}
                  data={treeData.value}
                  default-checked-keys={data.menus || []}
                  onCheck={treeClick}
                  check-strictly
                >
                  {{
                    default: (data) => {
                      return <span>{data.data.meta.title}</span>
                    }
                  }}
                </ElTree>
              </div>
            </>
          )
        }
      }
    }
  }
])

const treeClick = (item) => {
  const tree = unref(treeRef)
  const node = tree?.getNode(item)
  const isChecked = node.checked
  updateTreeState(node, isChecked, tree)
}

const updateTreeState = (node, isChecked, tree) => {
  // 更新子节点的选中状态
  updateChildrenState(node, isChecked, tree)

  // 更新父节点的选中状态
  updateParentState(node, tree)
}

const updateChildrenState = (node, isChecked, tree) => {
  tree?.setChecked(node.data.id, isChecked)

  if (node.childNodes) {
    node.childNodes.forEach((child) => {
      updateChildrenState(child, isChecked, tree)
    })
  }
}

const updateParentState = (node, tree) => {
  const parent = node.parent
  if (!parent) return

  const siblings = parent.childNodes
  const checked = siblings.some((sibling) => sibling.checked)

  if (checked) {
    tree.setChecked(parent.data.id, true, false)
  } else {
    // tree.setChecked(parent.data.id, false)
  }

  updateParentState(parent, tree)
}

const rules = reactive({
  name: [required()],
  role: [required()],
  status: [required()],
  level: [required()]
})

const { formRegister, formMethods } = useForm()
const { setValues, getFormData, getElFormExpose } = formMethods

const treeData = ref([])

const getMenuList = async () => {
  const res = await getMenuListApi({})
  if (res) {
    treeData.value = res.data.list
  }
}
getMenuList()

const submit = async () => {
  const elForm = await getElFormExpose()
  const valid = await elForm?.validate().catch((err) => {
    console.log(err)
  })
  if (valid) {
    const formData = await getFormData()
    const checkedKeys = unref(treeRef)?.getCheckedKeys() || []
    const halfCheckedKeys = unref(treeRef)?.getHalfCheckedKeys() || []
    const allCheckedKeys = [...checkedKeys, ...halfCheckedKeys]
    formData.menus = allCheckedKeys
    return formData
  }
}

watch(
  () => props.currentRow,
  (currentRow) => {
    if (!currentRow) return
    setValues(currentRow)
  },
  {
    deep: true,
    immediate: true
  }
)

defineExpose({
  submit
})
</script>

<template>
  <Form :rules="rules" @register="formRegister" :schema="formSchema" />
</template>
