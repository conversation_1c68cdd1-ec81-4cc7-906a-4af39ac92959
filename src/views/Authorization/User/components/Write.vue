<script setup lang="ts">
import { Form, FormSchema } from '@/components/Form'
import { useForm } from '@/hooks/web/useForm'
import { reactive, watch, ref } from 'vue'
import { useValidator } from '@/hooks/web/useValidator'
import { getAuthRoleApi } from '@/api/role'

const { required } = useValidator()

const props = defineProps({
  currentRow: {
    type: Object,
    default: () => undefined
  }
  // formSchema: {
  //   type: Array as PropType<FormSchema[]>,
  //   default: () => []
  // }
})

const formSchema = ref<FormSchema[]>([
  {
    field: 'name',
    label: '姓名',
    component: 'Input'
  },
  {
    field: 'expired_at',
    label: '账号有效期',
    component: 'DatePicker',
    componentProps: {
      type: 'datetime',
      valueFormat: 'YYYY-MM-DD HH:mm:ss'
    }
  },
  {
    field: 'username',
    label: '登录用户名',
    component: 'Input'
  },
  {
    field: 'password',
    label: '登录密码',
    component: 'InputPassword'
  },
  {
    field: 'remaining_available_times',
    label: '剩余积分',
    component: 'InputNumber'
  },
  {
    field: 'role_id',
    label: '角色',
    component: 'Select',
    // componentProps: {
    //   multiple: true,
    //   collapseTags: true,
    //   maxCollapseTags: 1,
    //   multipleLimit: 1
    // },
    optionApi: async () => {
      const res = await getAuthRoleApi()
      return res.data?.map((v) => ({
        label: v.name,
        value: v.id
      }))
    }
  },
  {
    field: 'ip_whitelist',
    label: 'IP白名单',
    component: 'Input',
    componentProps: {
      type: 'textarea',
      placeholder: '请输入IP地址，每个IP占一行'
    }
  }
])

const rules = reactive({
  username: [required()],
  name: [required()],
  password: [required()],
  roles: [required()],
  role_id: [required()]
})

const { formRegister, formMethods } = useForm()
const { setValues, getFormData, getElFormExpose, setSchema } = formMethods

const submit = async () => {
  const elForm = await getElFormExpose()
  const valid = await elForm?.validate().catch((err) => {
    console.log(err)
  })
  if (valid) {
    const formData = await getFormData()
    return formData
  }
}

watch(
  () => props.currentRow,
  (currentRow) => {
    if (!currentRow) return
    setValues(currentRow)
    // setSchema([
    //   {
    //     field: 'password',
    //     path: 'componentProps.disabled',
    //     value: true
    //   }
    // ])
    if (currentRow.is_super) {
      setSchema([
        {
          field: 'expired_at',
          path: 'componentProps.disabled',
          value: true
        }
      ])
    }
    rules.password = []
  },
  {
    deep: true,
    immediate: true
  }
)

defineExpose({
  submit
})
</script>

<template>
  <Form :rules="rules" @register="formRegister" :schema="formSchema" />
</template>
