<script setup lang="tsx">
import { ContentWrap } from '@/components/ContentWrap'
import { useI18n } from '@/hooks/web/useI18n'
import { Table } from '@/components/Table'
import { ref, unref, reactive } from 'vue'
import { ElMessage, ElMessageBox, ElSwitch, ElTag } from 'element-plus'
import {
  getUserList<PERSON><PERSON>,
  createUser<PERSON><PERSON>,
  updateUser<PERSON><PERSON>,
  deleteUserByIdA<PERSON>,
  unLockUserApi,
  updateUserStatusApi,
  exportUserA<PERSON>
} from '@/api/user'
import { useTable } from '@/hooks/web/useTable'
import { Search } from '@/components/Search'
import Write from './components/Write.vue'
import Detail from './components/Detail.vue'
import { Dialog } from '@/components/Dialog'
import { CrudSchema, useCrudSchemas } from '@/hooks/web/useCrudSchemas'
import { BaseButton } from '@/components/Button'
import { hasPermi } from '@/components/Permission'
import { useUserStore } from '@/store/modules/user'
const userStore = useUserStore()

const { t } = useI18n()

const { tableRegister, tableState, tableMethods } = useTable({
  fetchDataApi: async () => {
    // const { pageSize, currentPage } = tableState
    const res = await getUserListApi({
      // page: unref(currentPage),
      // size: unref(pageSize),
      ...unref(searchParams)
    })
    return {
      list: res.data || []
      // total: res.data.total || 0
    }
  },
  fetchDelApi: async () => {
    const res = await deleteUserByIdApi(unref(ids))
    return !!res
  }
})
const { loading, dataList, currentPage } = tableState
const { getList, getElTableExpose, delList } = tableMethods

const crudSchemas = reactive<CrudSchema[]>([
  {
    field: 'selection',
    search: {
      hidden: true
    },
    form: {
      hidden: true
    },
    detail: {
      hidden: true
    },
    table: {
      type: 'selection'
    }
  },
  {
    field: 'index',
    label: '序号',
    form: {
      hidden: true
    },
    search: {
      hidden: true
    },
    detail: {
      hidden: true
    },
    table: {
      type: 'index'
    }
  },
  {
    field: 'username',
    label: '用户名',
    minWidth: '150'
  },
  {
    field: 'name',
    label: '姓名',
    minWidth: '150'
  },
  {
    field: 'is_locked',
    label: '锁定状态',
    minWidth: '100',
    search: {
      hidden: true
    },
    slots: {
      default: ({ row }) => {
        return (
          <ElTag type={row.is_locked ? 'danger' : 'success'}>
            {row.is_locked ? '已锁定' : '未锁定'}
          </ElTag>
        )
      }
    }
  },
  {
    field: 'status',
    label: '账号状态',
    minWidth: '100',
    search: {
      hidden: true
    },
    slots: {
      default: ({ row }) => {
        return (
          <ElSwitch
            v-model={row.status}
            inline-prompt
            active-text="已启用"
            inactive-text="已禁用"
            active-value={1}
            inactive-value={0}
            disabled={!hasPermi('/user/updateStatus')}
            onChange={(value: number) => {
              updateUserStatus(row.id, value)
            }}
          />
        )
      }
    }
  },
  {
    field: 'role.name',
    label: '角色',
    minWidth: '150',
    search: {
      hidden: true
    }
  },
  {
    field: 'expired_at',
    label: '账号过期时间',
    minWidth: '150',
    search: {
      hidden: true
    },
    slots: {
      default: ({ row }) => {
        if (row.is_super === true) {
          return '永久'
        }
        return row.expired_at
      }
    }
  },
  {
    field: 'remaining_available_times',
    label: '剩余积分',
    minWidth: '130',
    search: {
      hidden: true
    }
  },
  {
    field: 'available_times_records_sum_number',
    label: '已使用积分',
    minWidth: '130'
  },
  {
    field: 'created_user.name',
    label: '创建人',
    minWidth: '150',
    search: {
      hidden: true
    }
  },
  {
    field: 'created_at',
    label: '创建时间',
    minWidth: '170',
    form: {
      hidden: true
    },
    detail: {
      hidden: true
    },
    search: {
      hidden: true
    }
  },
  {
    field: 'last_login_at',
    label: '最近登录时间',
    minWidth: '170',
    form: {
      hidden: true
    },
    detail: {
      hidden: true
    },
    search: {
      hidden: true
    }
  },
  {
    field: 'action',
    label: '操作',
    form: {
      hidden: true
    },
    detail: {
      hidden: true
    },
    search: {
      hidden: true
    },
    table: {
      fixed: 'right',
      width: 150, // 增加宽度以容纳新按钮
      slots: {
        default: (data: any) => {
          const row = data.row
          return (
            <>
              {hasPermi('/user/update') && (
                <BaseButton type="primary" size="small" onClick={() => action(row, 'edit')}>
                  编辑
                </BaseButton>
              )}
              {/* <BaseButton type="success" onClick={() => action(row, 'detail')}>
                详情
              </BaseButton> */}

              {hasPermi('/user/unlock') && row.is_locked === 1 && (
                <BaseButton type="warning" size="small" onClick={() => unLockUser(row)}>
                  解锁
                </BaseButton>
              )}
            </>
          )
        }
      }
    }
  }
])

const { allSchemas } = useCrudSchemas(crudSchemas)

const searchParams = ref({})
const setSearchParams = (params: any) => {
  currentPage.value = 1
  searchParams.value = params
  getList()
}

// const currentNodeKey = ref('')

const dialogVisible = ref(false)
const dialogTitle = ref('')

const currentRow = ref()
const actionType = ref('')

const AddAction = () => {
  dialogTitle.value = '新增'
  currentRow.value = undefined
  dialogVisible.value = true
  actionType.value = ''
}

const delLoading = ref(false)
const ids = ref<string[]>([])

const delData = async (row: any = null) => {
  const elTableExpose = await getElTableExpose()
  ids.value = row ? [row.id] : elTableExpose?.getSelectionRows().map((v) => v.id) || []
  if (ids.value.length <= 0) {
    ElMessage.error({
      message: '请至少选择一条记录'
    })
    return
  }
  delLoading.value = true

  await delList(unref(ids).length).finally(() => {
    delLoading.value = false
  })
}

const action = (row, type: string) => {
  dialogTitle.value = t(type === 'edit' ? 'exampleDemo.edit' : 'exampleDemo.detail')
  actionType.value = type
  currentRow.value = { ...row }
  dialogVisible.value = true
}

const writeRef = ref<ComponentRef<typeof Write>>()

const saveLoading = ref(false)

const save = async () => {
  const write = unref(writeRef)
  const formData = await write?.submit()
  if (formData) {
    if (formData.id && formData.password) {
      await ElMessageBox.confirm('您正在修改该用户的密码，确定执行吗？', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      })
    }
    saveLoading.value = true

    try {
      if (formData.id) {
        await updateUserApi(formData.id, formData)
      } else {
        await createUserApi(formData)
      }
      userStore.requestUserInfo()
      dialogVisible.value = false
      ElMessage.success('操作成功')
      getList()
    } finally {
      saveLoading.value = false
    }
  }
}

const unLockUser = async (row: any) => {
  try {
    await ElMessageBox.confirm('确定要解锁该用户吗？', '提示', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    })
    await unLockUserApi(row.id)
    ElMessage.success('用户解锁成功')
    getList() // 刷新列表
  } catch (error) {
    if (error !== 'cancel') {
      console.error('解锁用户失败:', error)
      ElMessage.error('解锁用户失败')
    }
  }
}

const updateUserStatus = async (id: number | string, status: number | string) => {
  await updateUserStatusApi(id, status)
  ElMessage.success('操作成功')
  getList()
}

const exportLoading = ref(false)

const exportAction = async () => {
  try {
    exportLoading.value = true

    // 获取当前搜索参数
    const params = unref(searchParams)

    // 调用导出API - 传递搜索参数以便后端过滤
    const response = (await exportUserApi(params)) as any

    // 创建下载链接 - 后端返回的是CSV文件
    const blob = new Blob([response.data], {
      type: 'text/csv; charset=utf-8'
    })
    const url = window.URL.createObjectURL(blob)
    const link = document.createElement('a')
    link.href = url

    // 从响应头中提取文件名，如果没有则使用默认文件名
    let filename = `用户数据导出_${new Date().toISOString().slice(0, 19).replace(/:/g, '-')}.csv`
    const contentDisposition = response.headers['content-disposition']
    if (contentDisposition) {
      // 处理 URL 编码的文件名
      const matches = contentDisposition.match(/filename="?([^"]+)"?/)
      if (matches && matches[1]) {
        filename = decodeURIComponent(matches[1])
      }
    }

    link.download = filename

    // 触发下载
    document.body.appendChild(link)
    link.click()
    document.body.removeChild(link)
    window.URL.revokeObjectURL(url)

    ElMessage.success('导出成功')
  } catch (error) {
    console.error('导出失败:', error)
    ElMessage.error('导出失败，请重试')
  } finally {
    exportLoading.value = false
  }
}
</script>

<template>
  <ContentWrap class="flex-[3] ml-20px">
    <Search :schema="allSchemas.searchSchema" @reset="setSearchParams" @search="setSearchParams" />

    <div class="mb-10px flex justify-between">
      <div>
        <BaseButton type="primary" @click="AddAction" v-hasPermi="`/user/add`">新增</BaseButton>
        <BaseButton
          :loading="delLoading"
          v-hasPermi="`/user/delete`"
          type="danger"
          @click="delData()"
        >
          删除
        </BaseButton>
      </div>
      <div>
        <BaseButton
          type="warning"
          :loading="exportLoading"
          @click="exportAction"
          v-hasPermi="`/user/export`"
        >
          导出
        </BaseButton>
      </div>
    </div>
    <Table
      :columns="allSchemas.tableColumns"
      :data="dataList"
      :loading="loading"
      default-expand-all
      node-key="id"
      @register="tableRegister"
      :show-pagination="false"
    />
  </ContentWrap>

  <Dialog v-model="dialogVisible" :title="dialogTitle">
    <Write
      v-if="actionType !== 'detail'"
      ref="writeRef"
      :form-schema="allSchemas.formSchema"
      :current-row="currentRow"
    />

    <Detail
      v-if="actionType === 'detail'"
      :detail-schema="allSchemas.detailSchema"
      :current-row="currentRow"
    />

    <template #footer>
      <BaseButton
        v-if="actionType !== 'detail'"
        type="primary"
        :loading="saveLoading"
        @click="save"
      >
        保存
      </BaseButton>
      <BaseButton @click="dialogVisible = false">关闭</BaseButton>
    </template>
  </Dialog>
</template>
