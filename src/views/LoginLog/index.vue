<script setup lang="tsx">
import { reactive, ref, unref } from 'vue'
import { getLoginLogRecordApi } from '@/api/loginLog'
import { useTable } from '@/hooks/web/useTable'
import { Table } from '@/components/Table'
import { Search } from '@/components/Search'
import { ContentWrap } from '@/components/ContentWrap'
import { CrudSchema, useCrudSchemas } from '@/hooks/web/useCrudSchemas'
import { FormSchema } from '@/components/Form'
import { getAuthListAPI } from '@/api/user'

const { tableRegister, tableState, tableMethods } = useTable({
  fetchDataApi: async () => {
    const { pageSize, currentPage } = tableState
    const res = await getLoginLogRecordApi({
      page: unref(currentPage),
      size: unref(pageSize),
      ...unref(searchParams)
    })
    return {
      list: res.data.items || [],
      total: res.data.total
    }
  }
})

const { total, loading, dataList, pageSize, currentPage } = tableState
const { getList } = tableMethods

const crudSchemas = reactive<CrudSchema[]>([
  {
    field: 'selection',
    search: {
      hidden: true
    },
    form: {
      hidden: true
    },
    detail: {
      hidden: true
    },
    table: {
      type: 'selection'
    }
  },
  {
    field: 'index',
    label: '序号',
    type: 'index',
    search: {
      hidden: true
    }
  },
  {
    field: 'user_id',
    label: '用户ID'
  },
  {
    field: 'username',
    label: '登录用户名',
    search: {
      hidden: true
    }
  },
  {
    field: 'login_ip',
    label: '登录IP',
    search: {
      hidden: true
    }
  },
  {
    field: 'created_at',
    label: '登录时间',
    search: {
      hidden: true
    }
  }
])

const { allSchemas } = useCrudSchemas(crudSchemas)

const searchSchemas = reactive<FormSchema[]>([
  {
    field: 'username',
    label: '登录用户名',
    component: 'Input'
  },
  // {
  //   field: 'user_id',
  //   label: '登录人',
  //   component: 'Select',
  //   componentProps: {
  //     nodeKey: 'id',
  //     props: {
  //       value: 'id',
  //       label: 'name'
  //     }
  //   },
  //   optionApi: async () => {
  //     const { data } = await getAuthListAPI()
  //     return data
  //   }
  // },
  {
    field: 'created_at',
    label: '登录时间',
    component: 'DatePicker',
    componentProps: {
      type: 'datetimerange',
      valueFormat: 'YYYY-MM-DD HH:mm:ss'
    }
  }
])

const searchParams = ref({})
const setSearchParams = (data: any) => {
  searchParams.value = data
  getList()
}
</script>

<template>
  <ContentWrap>
    <Search :schema="searchSchemas" @reset="setSearchParams" @search="setSearchParams" />
    <Table
      v-model:current-page="currentPage"
      v-model:page-size="pageSize"
      :columns="allSchemas.tableColumns"
      :data="dataList"
      :loading="loading"
      @register="tableRegister"
      :pagination="{
        total
      }"
    />
  </ContentWrap>
</template>
