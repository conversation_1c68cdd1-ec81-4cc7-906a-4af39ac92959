<script setup lang="tsx">
import { reactive, ref, unref } from 'vue'
import { getAvailableTimesRecordApi } from '@/api/availableTimesRecord'
import { useTable } from '@/hooks/web/useTable'
import { Table } from '@/components/Table'
import { Search } from '@/components/Search'
import { ContentWrap } from '@/components/ContentWrap'
import { CrudSchema, useCrudSchemas } from '@/hooks/web/useCrudSchemas'
// import { BaseButton } from '@/components/Button'
// import { useRouter } from 'vue-router'
// import { ElTag } from 'element-plus'
// import { searchTypes } from '@/constants/searchType'
import { FormSchema } from '@/components/Form'
import { getAuthListAPI } from '@/api/user'

const { tableRegister, tableState, tableMethods } = useTable({
  fetchDataApi: async () => {
    const { pageSize, currentPage } = tableState
    const res = await getAvailableTimesRecordApi({
      page: unref(currentPage),
      size: unref(pageSize),
      ...unref(searchParams)
    })
    return {
      list: res.data.items || [],
      total: res.data.total
    }
  }
})

const { total, loading, dataList, pageSize, currentPage } = tableState
const { getList } = tableMethods

const crudSchemas = reactive<CrudSchema[]>([
  {
    field: 'selection',
    search: {
      hidden: true
    },
    form: {
      hidden: true
    },
    detail: {
      hidden: true
    },
    table: {
      type: 'selection'
    }
  },
  {
    field: 'index',
    label: '序号',
    type: 'index',
    search: {
      hidden: true
    }
  },
  {
    field: 'user.username',
    label: '变更用户',
    search: {
      hidden: true
    }
  },
  {
    field: 'number',
    label: '变更数量',
    search: {
      hidden: true
    }
  },
  {
    field: 'changed_number',
    label: '变更后的数量',
    search: {
      hidden: true
    }
  },
  {
    field: 'note',
    label: '备注',
    search: {
      hidden: true
    }
  },
  {
    field: 'created_at',
    label: '变更时间',
    search: {
      hidden: true
    }
  }
])

const { allSchemas } = useCrudSchemas(crudSchemas)

const searchSchemas = reactive<FormSchema[]>([
  {
    field: 'user_id',
    label: '变更人',
    component: 'Select',
    componentProps: {
      nodeKey: 'id',
      props: {
        value: 'id',
        label: 'name'
      }
    },
    optionApi: async () => {
      const { data } = await getAuthListAPI()
      return data
    }
  },
  {
    field: 'note',
    label: '备注',
    component: 'Input'
  },
  {
    field: 'time',
    label: '变更时间',
    component: 'DatePicker',
    componentProps: {
      type: 'datetimerange',
      valueFormat: 'YYYY-MM-DD HH:mm:ss'
    }
  }
])

const searchParams = ref({})
const setSearchParams = (data: any) => {
  searchParams.value = data
  getList()
}
</script>

<template>
  <ContentWrap>
    <Search :schema="searchSchemas" @reset="setSearchParams" @search="setSearchParams" />
    <Table
      v-model:current-page="currentPage"
      v-model:page-size="pageSize"
      :columns="allSchemas.tableColumns"
      :data="dataList"
      :loading="loading"
      @register="tableRegister"
      :pagination="{
        total
      }"
    />
  </ContentWrap>
</template>
