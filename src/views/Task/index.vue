<script setup lang="tsx">
import { reactive, ref, unref } from 'vue'
import {
  deleteTaskItemApi,
  getTaskItemListApi,
  deleteTaskItemByIdApi,
  clearTaskItemApi
} from '@/api/taskItem'
import { useTable } from '@/hooks/web/useTable'
import { Table } from '@/components/Table'
import { Search } from '@/components/Search'
import { ContentWrap } from '@/components/ContentWrap'
import { CrudSchema, useCrudSchemas } from '@/hooks/web/useCrudSchemas'
import { BaseButton } from '@/components/Button'

import { ElTag, ElDivider, ElMessageBox, ElMessage } from 'element-plus'
import { FormSchema } from '@/components/Form'
import { getAuthListAPI } from '@/api/user'
import dayjs from 'dayjs'

import DetailModule from './components/detail.vue'
// import AddModule from './components/add.vue'
import { useRouter } from 'vue-router'
import { createSearchRecordApi } from '@/api/searchRecord'

import { filterIsFinished, filterType, filterSource, filterStatus } from '@/utils/filter'

const { tableRegister, tableState, tableMethods } = useTable({
  fetchDataApi: async () => {
    const { pageSize, currentPage } = tableState
    const res = await getTaskItemListApi({
      page: unref(currentPage),
      size: unref(pageSize),
      ...unref(searchParams)
    })
    return {
      list: res.data.items || [],
      total: res.data.total
    }
  },
  fetchDelApi: async () => {
    const res = await deleteTaskItemByIdApi(unref(ids))
    return !!res
  }
})

const { total, loading, dataList, pageSize, currentPage } = tableState
const { getList, getElTableExpose, delList } = tableMethods

const detailRef = ref()

const delLoading = ref(false)
const clearLoading = ref(false)
const ids = ref<string[]>([])

const crudSchemas = reactive<CrudSchema[]>([
  {
    field: 'selection',
    search: {
      hidden: true
    },
    form: {
      hidden: true
    },
    detail: {
      hidden: true
    },
    table: {
      type: 'selection'
    }
  },
  {
    field: 'index',
    label: '序号',
    type: 'index',
    search: {
      hidden: true
    }
  },
  {
    field: 'type',
    label: '搜索来源',
    minWidth: '100',
    search: {
      hidden: true
    },
    slots: {
      default: (data: any) => {
        return (
          <>
            <ElTag type={filterSource(data.row.type, true)}>{filterSource(data.row.type)}</ElTag>
          </>
        )
      }
    }
  },
  {
    field: 'keyword',
    label: '搜索关键词',
    minWidth: '120'
  },
  {
    field: 'es_type',
    label: '搜索类型',
    minWidth: '100',
    search: {
      hidden: true
    },
    slots: {
      default: (data: any) => {
        return (
          <>
            <ElTag type={filterType(data.row.es_type, true)}>{filterType(data.row.es_type)}</ElTag>
          </>
        )
      }
    }
  },
  {
    field: 'search_type_name',
    label: '模块名称',
    minWidth: '150'
  },
  {
    field: 'search_fields',
    label: '搜索字段',
    minWidth: '200',
    slots: {
      default: ({ row }) => {
        if (!row.search_fields || !Array.isArray(row.search_fields)) {
          return null
        }

        const renderField = (field) => {
          // 处理嵌套数组的情况
          if (Array.isArray(field)) {
            return field.map((subField, subIndex) => (
              <ElTag key={`${subIndex}-${subField.name || subIndex}`}>
                {subField.name || '未知字段'}
              </ElTag>
            ))
          }
          // 处理单个对象的情况
          return <ElTag key={field.name || Math.random()}>{field.name || '未知字段'}</ElTag>
        }

        return row.search_fields.map((field, index) => renderField(field))
      }
    }
  },
  {
    field: 'countries',
    label: '国家/地区',
    minWidth: '100',
    search: {
      hidden: true
    },
    slots: {
      default: ({ row }) => {
        return row.countries.map((i) => <ElTag key={i.id}>{i.name}</ElTag>)
      }
    }
  },
  {
    field: 'languages',
    label: '语言',
    minWidth: '100',
    search: {
      hidden: true
    },
    slots: {
      default: ({ row }) => {
        return row.languages.map((i) => <ElTag key={i.id}>{i.name}</ElTag>)
      }
    }
  },
  // {
  //   field: 'is_finished',
  //   label: '是否已完成',
  //   minWidth: '100',
  //   search: {
  //     hidden: true
  //   },
  //   slots: {
  //     default: (data: any) => {
  //       return (
  //         <>
  //           <ElTag type={filterIsFinished(data.row.is_finished, true)}>
  //             {filterIsFinished(data.row.is_finished)}
  //           </ElTag>
  //         </>
  //       )
  //     }
  //   }
  // },
  {
    field: 'status',
    label: '状态',
    search: {
      hidden: true
    },
    minWidth: '100',
    slots: {
      default: (data: any) => {
        return (
          <>
            <ElTag type={filterStatus(data.row.status, true)}>
              {filterStatus(data.row.status)}
            </ElTag>
          </>
        )
      }
    }
  },
  // {
  //   field: 'start_at',
  //   label: '开始时间',
  //   minWidth: '160',
  //   search: {
  //     hidden: true
  //   }
  // },
  // {
  //   field: 'finished_at',
  //   label: '完成时间',
  //   minWidth: '160',
  //   search: {
  //     hidden: true
  //   }
  // },
  // {
  //   field: 'expired_at',
  //   label: '过期时间',
  //   minWidth: '160',
  //   search: {
  //     hidden: true
  //   }
  // },
  {
    field: 'hit_total',
    label: '命中数量',
    minWidth: '100',
    search: {
      hidden: true
    }
  },
  {
    field: 'timeConsuming',
    label: '耗时（秒）',
    minWidth: '100',
    search: {
      hidden: true
    },
    slots: {
      default: (data: any) => {
        const { start_at, finished_at } = data.row
        let consuming: string = '-'
        if (start_at && finished_at) {
          const seconds = Math.round(dayjs(finished_at).diff(dayjs(start_at), 'second', true))
          consuming = seconds === 0 ? '0.01' : seconds.toString()
        }
        return consuming
      }
    }
  },
  {
    field: 'user.username',
    label: '操作人',
    minWidth: '120',
    search: {
      hidden: true
    }
  },

  {
    field: 'action',
    label: '操作',
    search: {
      hidden: true
    },
    fixed: 'right',
    minWidth: '100',
    slots: {
      default: (data: any) => {
        const row = data.row
        return (
          <>
            <BaseButton type="primary" size="small" onClick={() => detail(row)}>
              详情
            </BaseButton>
          </>
        )
      }
    }
  }
])

const { allSchemas } = useCrudSchemas(crudSchemas)

const searchSchemas = reactive<FormSchema[]>([
  {
    field: 'keyword',
    label: '任务关键词',
    component: 'InputTag',
    componentProps: {
      placeholder: '请输入关键词，按回车添加',
      clearable: true
    }
  },
  {
    field: 'user_id',
    label: '搜索人',
    component: 'Select',
    componentProps: {
      nodeKey: 'id',
      props: {
        value: 'id',
        label: 'name'
      }
    },
    optionApi: async () => {
      const { data } = await getAuthListAPI()
      return data
    }
  },
  {
    field: 'start_at',
    label: '开始时间',
    component: 'DatePicker',
    componentProps: {
      type: 'datetimerange',
      valueFormat: 'YYYY-MM-DD HH:mm:ss'
    }
  },
  {
    field: 'finished_at',
    label: '完成时间',
    component: 'DatePicker',
    componentProps: {
      type: 'datetimerange',
      valueFormat: 'YYYY-MM-DD HH:mm:ss'
    }
  },
  {
    field: 'status',
    label: '状态',
    component: 'Select',
    componentProps: {
      options: [
        {
          label: '等待执行',
          value: 0
        },
        {
          label: '执行中',
          value: 1
        },
        {
          label: '已完成',
          value: 2
        },
        {
          label: '异常',
          value: 3
        }
      ]
    }
  },
  // {
  //   field: 'is_expired',
  //   label: '是否已过期',
  //   component: 'Select',
  //   componentProps: {
  //     options: [
  //       {
  //         label: '是',
  //         value: 1
  //       },
  //       {
  //         label: '否',
  //         value: 0
  //       }
  //     ]
  //   }
  // },
  {
    field: 'type',
    label: '搜索来源',
    component: 'Select',
    componentProps: {
      options: [
        {
          label: '普通搜索',
          value: 1
        },
        {
          label: '任务布控',
          value: 2
        }
      ]
    }
  },
  {
    field: 'is_hit',
    label: '是否命中',
    component: 'Select',
    componentProps: {
      options: [
        {
          label: '是',
          value: 1
        },
        {
          label: '否',
          value: 0
        }
      ]
    }
  }
])

const searchParams = ref({})
const setSearchParams = (data: any) => {
  searchParams.value = data
  getList()
}

const detail = (row) => {
  detailRef.value.open(row.id)
}

// const deleteRecord = (row) => {
//   ElMessageBox.confirm('是否删除此任务及缓存', '提示', {
//     confirmButtonText: '确定',
//     cancelButtonText: '取消',
//     type: 'warning'
//   })
//     .then(async () => {
//       const id = row.id
//       await deleteTaskApi(id).finally(() => {})

//       getList()
//     })
//     .catch(() => {})
// }

const delData = async (row: any = null) => {
  const elTableExpose = await getElTableExpose()
  ids.value = row ? [row.id] : elTableExpose?.getSelectionRows().map((v) => v.id) || []
  if (ids.value.length <= 0) {
    ElMessage.error({
      message: '请至少选择一条记录'
    })
    return
  }
  delLoading.value = true

  await delList(unref(ids).length).finally(() => {
    delLoading.value = false
  })
}

const clearData = async () => {
  clearLoading.value = true

  ElMessageBox.confirm('是否清空所有任务', '提示', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning'
  })
    .then(async () => {
      await clearTaskItemApi(searchParams.value)
      getList()
      ElMessage.success('清空成功')
    })
    .finally(() => {
      clearLoading.value = false
    })
}

const router = useRouter()

const searchTo = (row) => {
  const apiParams = {
    keyword: row.keyword,
    searchField: row.search_field.key,
    searchCountries: row.countries.map((i) => i.id),
    searchLanguages: row.languages.map((i) => i.id),
    esType: row.es_type,
    type: 1
  }

  // 记录日志
  createSearchRecordApi(apiParams)

  const resultTo = router.resolve({
    name: 'Result',
    query: apiParams
  })
  window.open(resultTo.href, '_blank')
}
</script>

<template>
  <ContentWrap>
    <Search :schema="searchSchemas" @reset="setSearchParams" @search="setSearchParams" />

    <ElDivider />

    <div class="mb-10px">
      <BaseButton :loading="delLoading" type="danger" @click="delData()"> 删除 </BaseButton>
      <BaseButton :loading="clearLoading" type="warning" @click="clearData()"> 清空 </BaseButton>
    </div>
    <Table
      v-model:current-page="currentPage"
      v-model:page-size="pageSize"
      :columns="allSchemas.tableColumns"
      :data="dataList"
      :loading="loading"
      @register="tableRegister"
      :pagination="{
        total
      }"
    />

    <DetailModule ref="detailRef" />
  </ContentWrap>
</template>
