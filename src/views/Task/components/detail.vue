<template>
  <div>
    <ElDrawer v-model="show" title="任务详情" size="60%">
      <div>
        <ElSkeleton :loading="loading" animated>
          <ElDescriptions title="基本信息" border>
            <ElDescriptionsItem label="关键词">{{ task.keyword }}</ElDescriptionsItem>
            <ElDescriptionsItem label="搜索类型">{{ filterType(task.es_type) }}</ElDescriptionsItem>
            <ElDescriptionsItem label="命中数量">{{ task.hit_total }}</ElDescriptionsItem>
            <ElDescriptionsItem label="模块名称">{{ task.search_type_name }}</ElDescriptionsItem>
            <ElDescriptionsItem label="国家">{{
              task.countries.map((i) => i.name).join(',')
            }}</ElDescriptionsItem>
            <ElDescriptionsItem label="语言">{{
              task.languages.map((i) => i.name).join(',')
            }}</ElDescriptionsItem>
            <ElDescriptionsItem label="开始时间">{{ task.start_at }}</ElDescriptionsItem>
            <ElDescriptionsItem label="结束时间">{{ task.finished_at }}</ElDescriptionsItem>
            <ElDescriptionsItem label="过期时间">
              <!-- 编辑模式 -->
              <div v-if="editingExpiredAt" style="display: flex; align-items: center">
                <ElDatePicker
                  v-model="newExpiredAt"
                  type="datetime"
                  placeholder="选择过期时间"
                  format="YYYY-MM-DD HH:mm:ss"
                  value-format="YYYY-MM-DD HH:mm:ss"
                  @change="saveExpiredAt"
                />
                <ElButton type="text" class="ml-2" @click="cancelEditExpiredAt">取消</ElButton>
              </div>
              <!-- 查看模式 -->
              <div v-else style="display: flex; align-items: center">
                <span>{{ task.expired_at }}</span>
                <ElIcon class="ml-2" @click="editExpiredAt">
                  <Edit />
                </ElIcon>
              </div>
            </ElDescriptionsItem>
            <ElDescriptionsItem label="是否已过期">
              <ElTag :type="dayjs(task.expired_at) < dayjs() ? 'success' : 'danger'">{{
                dayjs(task.expired_at) < dayjs() ? '是' : '否'
              }}</ElTag>
            </ElDescriptionsItem>
          </ElDescriptions>
        </ElSkeleton>
      </div>
      <template #footer>
        <div>
          <BaseButton type="primary" @click="confirm">确定</BaseButton>
        </div>
      </template>
    </ElDrawer>
  </div>
</template>

<script setup>
import {
  ElDrawer,
  ElSkeleton,
  ElDescriptions,
  ElDescriptionsItem,
  ElTag,
  ElTable,
  ElTableColumn,
  ElDatePicker, // 引入日期时间选择器
  ElButton, // 引入按钮组件
  ElIcon,
  ElInput, // 引入输入框组件
  ElMessage
} from 'element-plus'
import { Edit } from '@element-plus/icons-vue' // 引入编辑图标
import { ref } from 'vue'
import { getTaskItemDetailApi, putTaskItemApi } from '@/api/taskItem'
import dayjs from 'dayjs'
import { filterIsFinished, filterType } from '@/utils/filter'

const show = ref(false)
const loading = ref(false)
const task = ref({}) // 初始化为空对象
const editingExpiredAt = ref(false) // 是否处于编辑模式
const newExpiredAt = ref('') // 新的过期时间

const open = async (id) => {
  show.value = true
  loading.value = true
  const { data } = await getTaskItemDetailApi(id)
  task.value = data
  newExpiredAt.value = task.value.expired_at // 初始化新的过期时间
  loading.value = false
}

const confirm = () => {
  show.value = false
}

// 切换到编辑模式
const editExpiredAt = () => {
  editingExpiredAt.value = true
}

// 取消编辑
const cancelEditExpiredAt = () => {
  editingExpiredAt.value = false // 退出编辑模式
  newExpiredAt.value = task.value.expired_at // 恢复原始的过期时间
}

// 保存新的过期时间
const saveExpiredAt = async () => {
  task.value.expired_at = newExpiredAt.value // 更新任务的过期时间
  await putTaskItemApi(task.value.id, {
    expired_at: newExpiredAt.value
  })
  ElMessage.success('操作成功')
  editingExpiredAt.value = false // 退出编辑模式
}

defineExpose({
  open
})
</script>

<style lang="scss" scoped>
/* 自定义样式 */
.ml-2 {
  margin-left: 8px;
  cursor: pointer;
}
</style>
