<script setup lang="ts">
import { ElRow, ElCol, ElCard, ElSkeleton, ElIcon } from 'element-plus'
import { CountTo } from '@/components/CountTo'
import { useDesign } from '@/hooks/web/useDesign'
import { ref, reactive } from 'vue'

import { User, Lock, MessageBox, Files } from '@element-plus/icons-vue'

const props = defineProps({
  totalState: {
    type: Object,
    default: () => {}
  }
})

// import { getCountApi } from '@/api/dashboard/analysis'
// export type AnalysisTotalTypes = {
//   users: number
//   messages: number
//   moneys: number
//   shoppings: number
// }

const { getPrefixCls } = useDesign()

const prefixCls = getPrefixCls('panel')

const loading = ref(false)
</script>

<template>
  <ElRow :gutter="20" justify="space-between">
    <ElCol :span="5">
      <ElCard shadow="hover">
        <ElSkeleton :loading="loading" animated :rows="2">
          <template #default>
            <div>
              <!-- <div>
                <div
                  :class="`${prefixCls}__item--icon ${prefixCls}__item--shopping p-16px inline-block rounded-6px`"
                >
                  <ElIcon :size="40"><MessageBox /></ElIcon>
                </div>
              </div> -->
              <div class="flex flex-col justify-center items-center">
                <div :class="`${prefixCls}__item--text text-16px text-gray-500 text-center`">
                  终端数量
                </div>
                <CountTo
                  class="text-20px font-700 text-center mt-2"
                  :start-val="0"
                  :end-val="totalState?.sysTotal"
                  :duration="2600"
                />
              </div>
            </div>
          </template>
        </ElSkeleton>
      </ElCard>
    </ElCol>
    <ElCol :span="5">
      <ElCard shadow="hover">
        <ElSkeleton :loading="loading" animated :rows="2">
          <template #default>
            <div>
              <!-- <div>
                <div
                  :class="`${prefixCls}__item--icon ${prefixCls}__item--peoples p-16px inline-block rounded-6px`"
                >
                  <ElIcon :size="40"><Files /></ElIcon>
                </div>
              </div> -->
              <div class="flex flex-col justify-center items-center">
                <div :class="`${prefixCls}__item--text text-16px text-gray-500 text-center`">
                  IP数量
                </div>
                <CountTo
                  class="text-20px font-700 text-center mt-2"
                  :start-val="0"
                  :end-val="totalState?.ipTotal"
                  :duration="2600"
                />
              </div>
            </div>
          </template>
        </ElSkeleton>
      </ElCard>
    </ElCol>

    <ElCol :span="5">
      <ElCard shadow="hover">
        <ElSkeleton :loading="loading" animated :rows="2">
          <template #default>
            <div>
              <!-- <div>
                <div
                  :class="`${prefixCls}__item--icon ${prefixCls}__item--message p-16px inline-block rounded-6px`"
                >
                  <ElIcon :size="40"><User /></ElIcon>
                </div>
              </div> -->
              <div class="flex flex-col justify-center items-center">
                <div :class="`${prefixCls}__item--text text-16px text-gray-500 text-center`">
                  域名数量
                </div>
                <CountTo
                  class="text-20px font-700 text-center mt-2"
                  :start-val="0"
                  :end-val="totalState?.urlTotal"
                  :duration="2600"
                />
              </div>
            </div>
          </template>
        </ElSkeleton>
      </ElCard>
    </ElCol>

    <ElCol :span="5">
      <ElCard shadow="hover">
        <ElSkeleton :loading="loading" animated :rows="2">
          <template #default>
            <div>
              <!-- <div>
                <div
                  :class="`${prefixCls}__item--icon ${prefixCls}__item--money p-16px inline-block rounded-6px`"
                >
                  <ElIcon :size="40"><Lock /></ElIcon>
                </div>
              </div> -->
              <div class="flex flex-col justify-center items-center">
                <div :class="`${prefixCls}__item--text text-16px text-gray-500 text-center`">
                  密码数量
                </div>
                <CountTo
                  class="text-20px font-700 text-center mt-2"
                  :start-val="0"
                  :end-val="totalState?.passwordTotal"
                  :duration="2600"
                />
              </div>
            </div>
          </template>
        </ElSkeleton>
      </ElCard>
    </ElCol>

    <ElCol :span="4">
      <ElCard shadow="hover">
        <ElSkeleton :loading="loading" animated :rows="2">
          <template #default>
            <div>
              <!-- <div>
                <div
                  :class="`${prefixCls}__item--icon ${prefixCls}__item--money p-16px inline-block rounded-6px`"
                >
                  <ElIcon :size="40"><Lock /></ElIcon>
                </div>
              </div> -->
              <div class="flex flex-col justify-center items-center">
                <div :class="`${prefixCls}__item--text text-16px text-gray-500 text-center`">
                  文件数量
                </div>
                <CountTo
                  class="text-20px font-700 text-center mt-2"
                  :start-val="0"
                  :end-val="totalState?.fileTotal"
                  :duration="2600"
                />
              </div>
            </div>
          </template>
        </ElSkeleton>
      </ElCard>
    </ElCol>
  </ElRow>
</template>

<style lang="less" scoped>
@prefix-cls: ~'@{adminNamespace}-panel';

.@{prefix-cls} {
  &__item {
    &--peoples {
      color: #40c9c6;
    }

    &--message {
      color: #36a3f7;
    }

    &--money {
      color: #f4516c;
    }

    &--shopping {
      color: #34bfa3;
    }
  }
}
</style>
