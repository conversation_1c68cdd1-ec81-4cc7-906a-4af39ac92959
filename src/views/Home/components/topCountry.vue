<template>
  <ElSkeleton :loading="loading" animated class="h-full">
    <Echart :options="pieOptionsData" class="h-full" />
  </ElSkeleton>
</template>

<script setup>
import { Echart } from '@/components/Echart'
import { onMounted, reactive, ref } from 'vue'
import { ElSkeleton } from 'element-plus'
import { getTopCountryApi } from '@/api/dashboard'

const loading = ref(false)
const countryList = ref([])
const pcTotalList = ref([])

const pieOptionsData = reactive({
  tooltip: {
    trigger: 'axis',
    axisPointer: {
      type: 'shadow'
    },
    formatter: (params) => {
      let relValue = params[0].name
      return (relValue +=
        '<br />' +
        params[0].marker +
        'PC量：' +
        (params[0].value > 10000
          ? (params[0].value / 10000).toFixed(2) + '万'
          : params[0].value.toLocaleString()) +
        ' 台')
    }
  },
  grid: {
    top: '5%',
    left: '8%',
    right: '5%',
    bottom: '5%',
    containLabel: true
  },
  yAxis: {
    type: 'category',
    data: countryList,
    axisTick: {
      alignWithLabel: true
    }
  },
  xAxis: {
    type: 'value'
  },
  series: [
    {
      name: 'PC量',
      data: pcTotalList,
      type: 'bar'
    }
  ]
})

const getTopCountry = async () => {
  loading.value = true
  const { data } = await getTopCountryApi()
  countryList.value = data.countryList
  pcTotalList.value = data.pcTotalList
  loading.value = false
}

onMounted(() => {
  getTopCountry()
})
</script>

<style lang="scss" scoped>
:deep(.el-skeleton) {
  height: 100%;
}
</style>
