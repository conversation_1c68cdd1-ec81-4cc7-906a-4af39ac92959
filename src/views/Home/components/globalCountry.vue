<template>
  <ElSkeleton :loading="loading" animated class="h-full">
    <Echart :options="pieOptionsData" class="h-full" :geoJsonData="geoJsonData" />
  </ElSkeleton>
</template>

<script setup>
import { Echart } from '@/components/Echart'
import { onMounted, reactive, ref } from 'vue'
import { ElSkeleton } from 'element-plus'

import geoJsonData from '@/assets/json/world.json'
// import geoJsonData from '@surbowl/world-geo-json-zh'

import nameMap from './nameMap'

import { getGlobalCountryApi } from '@/api/dashboard'

const loading = ref(false)
const dbData = ref([])

const pieOptionsData = reactive({
  tooltip: {
    trigger: 'item',
    borderColor: '#666', //区域边框颜色
    formatter: function (params) {
      if (params.name) {
        return params.name + ' : ' + (isNaN(params.value) ? 0 : parseInt(params.value))
      }
    }
  },
  toolbox: {
    show: true,
    orient: 'vertical',
    left: 'right',
    top: 'center'
    // feature: {
    //   dataView: { readOnly: false },
    //   restore: {},
    //   saveAsImage: {}
    // }
  },
  visualMap: {
    min: 0, //最小值
    max: 1000000, //最大值
    // orient: 'horizontal', //图例排列方向
    orient: 'vertical', //图例模式
    left: 26,
    bottom: 20,
    showLabel: true, //显示图例文本
    precision: 0, //数据展示无小数点
    itemWidth: 12, //图例宽度
    itemHeight: 12, //图例高度
    textGap: 10, //图例间距
    inverse: false, //数据反向展示
    hoverLink: true, //鼠标悬浮
    inRange: {
      //选中图例后背景半透明
      color: ['rgba(3,4,5,0.4)'],
      symbol: 'rect' //更改图元样式
    },
    pieces: [
      {
        gt: 500001,
        label: '>500000',
        color: '#004bbc'
      },
      {
        gte: 200001,
        lte: 500000,
        label: '200001-500000',
        color: '#237bff'
      },
      {
        gte: 100000,
        lte: 200000,
        label: '100000-200000',
        color: '#35a9ff'
      },
      {
        gte: 10000,
        lte: 99999,
        label: '10000-99999',
        color: '#73c1ff'
      },
      {
        gte: 1,
        lte: 9999,
        label: '1-9999',
        color: '#b4deff'
      },
      {
        lte: 0,
        label: '0',
        color: '#d2ecf1'
      }
    ],
    textStyle: {
      color: '#fff',
      fontSize: 14, //图元字体大小
      fontWeight: 500
    }
  },
  series: [
    {
      name: '国家/地区PC量分布',
      type: 'map', //地图
      mapType: 'world',
      zoom: 1, //地图大小
      roam: false, //禁止拖拽
      itemStyle: {
        normal: {
          areaColor: '#d2ecf1', //地图默认颜色
          borderWidth: 0.5, //边框宽度
          textStyle: {
            color: '#fff' //默认文字颜色
          },
          borderColor: '#000' //地图边框颜色
        },
        emphasis: {
          areaColor: '#4306fe' //动态背景颜色
        }
      },
      select: {
        //地图选中颜色
        itemStyle: {
          areaColor: '#4306fe'
        }
      },
      label: {
        normal: {
          //静态的时候展示样式
          show: false, //是否显示地图名称
          textStyle: {
            color: '#000', //颜色
            fontSize: 14, //文字大小
            fontFamily: 'Arial'
          }
        },
        emphasis: {
          //动态展示的样式
          color: '#fff'
        }
      },
      data: dbData,
      nameMap: nameMap
    }
  ]
})

const getGlobalCountry = async () => {
  loading.value = true
  const { data } = await getGlobalCountryApi()
  dbData.value = data
  loading.value = false
}

onMounted(async () => {
  getGlobalCountry()
})
</script>

<style lang="scss" scoped>
:deep(.el-skeleton) {
  height: 100%;
}
</style>
