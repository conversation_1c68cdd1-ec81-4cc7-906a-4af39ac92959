<template>
  <ElRow :gutter="10">
    <ElCol :span="24">
      <ElInput size="large" v-model="keyword" clearable>
        <template #prepend>关键字</template>
        <template #append>
          <ElButton :icon="Search" @click="toSearch" />
        </template>
      </ElInput>
    </ElCol>
    <ElDivider />
    <ElCol :span="24">
      <ElAlert type="success" :closable="false">
        <template #title> 密码模块命中条数：{{ dataTotal }} </template>
      </ElAlert>
      <div v-if="isSearch">
        <ElSkeleton class="p-6" v-if="loading" :rows="5" animated />
        <div v-else>
          <div class="content" v-if="dataList && dataList.length > 0">
            <ContentWrap class="mt-3" v-for="(item, index) in dataList" :key="index">
              <Descriptions :column="2" :data="item" :schema="schema" />
            </ContentWrap>
            <ContentWrap class="flex justify-end mt-5">
              <ElPagination
                background
                layout="prev, pager, next"
                :total="dataTotal"
                :page-size="pageSize"
                :current-page="currentPage"
                @size-change="sizeChange"
                @current-change="currentChange"
              />
            </ContentWrap>
          </div>
          <ContentWrap class="mt-5" v-else>
            <ElEmpty description="没有可用的数据" />
          </ContentWrap>
        </div>
      </div>
    </ElCol>
  </ElRow>
</template>

<script setup lang="ts">
import {
  ElInput,
  ElAlert,
  ElPagination,
  ElEmpty,
  ElMessage,
  ElSkeleton,
  ElRow,
  ElCol,
  ElButton,
  ElDivider
} from 'element-plus'
import { ContentWrap } from '@/components/ContentWrap'
import { onMounted, reactive, ref } from 'vue'
import { Descriptions } from '@/components/Descriptions'
import { searchPasswordResultAPI } from '@/api/search'
import { useIcon } from '@/hooks/web/useIcon'

import { useRoute } from 'vue-router'
const { query } = useRoute()

const pageSize = ref(10)
const currentPage = ref(1)

const Search = useIcon({ icon: 'ep:search' })

const sizeChange = (size) => {
  pageSize.value = size
  searchContent()
}

const currentChange = (current) => {
  currentPage.value = current
  searchContent()
}

const loading = ref(false)
const isSearch = ref(true)
const keyword = ref('')

const dataList = ref([]) as any
const dataTotal = ref(0)

const toSearch = () => {
  if (!keyword.value) {
    ElMessage.error('请填写您需要搜索的关键字，暂时只支持关键词搜索')
    return
  }
  isSearch.value = true
  pageSize.value = 10
  currentPage.value = 1
  searchContent()
}

const searchContent = async () => {
  const from = (currentPage.value - 1) * pageSize.value
  const size = pageSize.value
  loading.value = true
  try {
    const { data } = await searchPasswordResultAPI({
      keyword: keyword.value,
      from,
      size
    })
    const { items, total } = data || {}

    dataList.value = items
    dataTotal.value = total
  } finally {
    loading.value = false
  }
}

onMounted(() => {
  const { keyword: kv } = query || {}
  keyword.value = kv as string

  toSearch()
})

const schema = reactive([
  {
    field: '_source.domain',
    label: '主页',
    width: '25%'
  },
  {
    field: '_source.url',
    label: 'URL',
    width: '25%'
  },
  {
    field: '_source.username',
    label: '用户名',
    width: '25%'
  },
  {
    field: '_source.password',
    label: '密码',
    width: '25%'
  },
  {
    field: '_source.sourcefile',
    label: '来源文件',
    width: '25%'
  },
  {
    field: '_source.line',
    label: '所在行数',
    width: '25%'
  }
])
</script>

<style lang="scss" scoped></style>
