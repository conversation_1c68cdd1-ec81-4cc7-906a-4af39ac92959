<template>
  <div class="search-page">
    <ElCard class="search-conditions">
      <template #header>
        <h2 class="card-title">搜索条件</h2>
      </template>

      <div class="global-search">
        <h3>1. 全局条件</h3>
        <div class="flex mt-4">
          <ElSelect
            multiple
            clearable
            v-model="conditions.searchCountries"
            placeholder="选择搜索国家/地区"
            class="select-item"
            style="width: 50%"
          >
            <template #header>
              <ElInput
                placeholder="请输入关键字进行搜索"
                v-model="countryInput"
                @input="countryFilter"
              />
            </template>
            <ElOption
              v-for="(item, key) in countriesList"
              :key="key"
              :value="item.id"
              :label="`${item.name}${item.code}`"
            />
          </ElSelect>

          <ElSelect
            multiple
            clearable
            v-model="conditions.searchLanguages"
            placeholder="选择搜索语言"
            class="select-item"
            style="width: 50%"
          >
            <template #header>
              <ElInput
                placeholder="请输入关键字进行搜索"
                v-model="languageInput"
                @input="languageFilter"
              />
            </template>
            <ElOption
              v-for="(item, key) in languagesList"
              :key="key"
              :value="item.id"
              :label="item.name"
            />
          </ElSelect>
        </div>
      </div>

      <div class="custom-conditions">
        <h3>2. 自定义条件</h3>
        <div class="mt-4">
          <div
            v-for="(condition, index) in conditions.list"
            :key="index"
            class="condition-item w-full flex gap-4"
          >
            <ElSelect
              clearable
              v-model="condition.searchField"
              placeholder="选择搜索类型"
              class="flex-1"
            >
              <ElOption
                v-for="(item, key) in fields"
                :key="key"
                :value="item.key"
                :label="item.name"
              />
            </ElSelect>

            <ElInput v-model="condition.keyword" placeholder="输入关键词" class="flex-1" />

            <div>
              <ElButton @click="addCondition" type="success" circle>
                <ElIcon><Plus /></ElIcon>
              </ElButton>

              <ElButton @click="removeCondition(index)" type="danger" circle>
                <ElIcon><Delete /></ElIcon>
              </ElButton>
            </div>
          </div>
        </div>
      </div>

      <div class="search-button w-full flex justify-center mt-4">
        <ElButton @click="performSearch" type="primary" class="w-full" :icon="Search">
          搜索
        </ElButton>
      </div>
    </ElCard>

    <ElCard class="search-results" v-if="isSearched">
      <template #header>
        <h2 class="card-title">搜索结果</h2>
      </template>

      <ElSkeleton class="p-6" :loading="loading" :rows="5" animated>
        <ElTable :data="dataList" style="width: 100%">
          <ElTableColumn prop="IP_Address" label="IP" />
          <!-- <ElTableColumn prop="modules" label="命中模块">
            <template #default="{ row }">
              <ElTag v-for="(item, index) in row.modules" :key="index" type="success" size="small">
                {{ item }}
              </ElTag>
            </template>
          </ElTableColumn> -->
          <ElTableColumn prop="Country" label="国家" />
          <ElTableColumn prop="Language" label="语言" />
          <ElTableColumn prop="user" label="用户名" />
          <ElTableColumn prop="OSVersion" label="操作系统" />
          <ElTableColumn label="操作">
            <template #default="{ row }">
              <ElButton
                type="primary"
                @click="handleDetail(row)"
                :icon="View"
                size="small"
                :loading="row.loading"
              >
                详情
              </ElButton>
            </template>
          </ElTableColumn>
        </ElTable>
        <div class="flex items-center mt-2">
          <ElPagination
            v-model:page-size="pagination.pageSize"
            v-model:current-page="pagination.currentPage"
            :page-sizes="[10, 50, 100]"
            :total="pageTotal"
            @size-change="sizeChange"
            @current-change="currentChange"
            layout="total, sizes, prev, pager, next, jumper"
          />
        </div>
      </ElSkeleton>
    </ElCard>
  </div>
</template>

<script setup>
import { onMounted, ref } from 'vue'
import { Delete, Plus, Search, ArrowLeft, ArrowRight, View } from '@element-plus/icons-vue'
import {
  ElButton,
  ElCard,
  ElIcon,
  ElInput,
  ElMessage,
  ElOption,
  ElSelect,
  ElSkeleton,
  ElTable,
  ElTableColumn,
  ElTag,
  ElPagination
} from 'element-plus'
import { getCountryList, getLanguageList } from '@/utils/cache'
import { postGlobalSearchAPI, getGlobalSearchAPI } from '@/api/global'

import { checkCidAuthFromPageApi } from '@/api/search'
import { getFieldsApi } from '@/api/home'
import { getPushConnection } from '@/utils/push'
import { useUserStore } from '@/store/modules/user'

const selectedCountry = ref('')
const selectedLanguage = ref('')
const searchConditions = ref([
  {
    esType: 5,
    keyword: '',
    searchField: null
  }
])

const pagination = ref({
  currentPage: 1,
  pageSize: 10
})

const pageTotal = ref(0)

const responseKey = ref(null)

const fields = ref([])

const dataList = ref([])

const isSearched = ref(false)
const loading = ref(false)

const sizeChange = (size) => {
  pagination.value.currentPage = 1
  pagination.value.pageSize = size
  getContent()
}

const currentChange = (currentPage) => {
  pagination.value.currentPage = currentPage
  getContent()
}

const searchContent = async () => {
  isSearched.value = true
  loading.value = true

  try {
    const { data } = await postGlobalSearchAPI(conditions.value)
    const { key, ifCompleted } = data || {}
    responseKey.value = key

    listenMessage()
    if (ifCompleted) {
      getContent()
    }
  } finally {
    // loading.value = false
  }
}

const listenMessage = () => {
  // 建立连接
  var connection = getPushConnection()
  const userStore = useUserStore()

  // 创建监听频道
  var user_channel = connection.subscribe('global-search-' + userStore.getUserInfo.id)
  if (responseKey.value) {
    user_channel.on('cid-list-' + responseKey.value, function (message) {
      // message是消息内容
      console.log('收到 glabal search message', message)
      getContent()
    })
  }
}

const getContent = async () => {
  loading.value = true
  try {
    const params = {
      page: pagination.value.currentPage,
      limit: pagination.value.pageSize,
      key: responseKey.value
    }
    const { data } = await getGlobalSearchAPI(params)
    const { total, items } = data || {}
    dataList.value = items
    pageTotal.value = total
  } finally {
    loading.value = false
  }
}

const addCondition = () => {
  conditions.value.list.push({
    esType: 5,
    keyword: '',
    searchField: null
  })
}

const removeCondition = (index) => {
  conditions.value.list.splice(index, 1)
}

const handleDetail = async (row) => {
  const cid = row.cid
  row.loading = true
  try {
    await checkCidAuthFromPageApi({ cid, source: 2 })
  } finally {
    row.loading = false
  }

  const href = '/home/<USER>/result/' + cid
  window.open(href, '_blank')
}

const performSearch = () => {
  for (const item of conditions.value.list) {
    if (!item.keyword) {
      ElMessage.error('请输入搜索关键词')
      return false
    }
    if (!item.searchField) {
      ElMessage.error('请选择搜索类型')
      return false
    }
  }

  dataList.value = []

  pagination.value.currentPage = 1
  searchContent()
}

// 新增条件
const conditions = ref({
  list: [
    {
      esType: 5,
      keyword: ''
    }
  ],
  searchCountries: [],
  searchLanguages: []
})

const countriesList = ref([])
const languagesList = ref([])

const originCountriesList = ref([])
const originLanguagesList = ref([])

const countryInput = ref(null)
const countryFilter = (val) => {
  if (!val) {
    countriesList.value = originCountriesList.value
  } else {
    countriesList.value = countriesList.value.filter((item) => {
      return item.name.indexOf(val) > -1 || item.code.toUpperCase().indexOf(val.toUpperCase()) > -1
    })
  }
}

const languageInput = ref(null)
const languageFilter = (val) => {
  if (!val) {
    languagesList.value = originLanguagesList.value
  } else {
    languagesList.value = languagesList.value.filter((item) => {
      return item.name.indexOf(val) > -1
    })
  }
}

const getCountriesData = async () => {
  const data = await getCountryList()

  countriesList.value = data
  originCountriesList.value = data
}

const getLanguagesData = async () => {
  const data = await getLanguageList()

  languagesList.value = data
  originLanguagesList.value = data
}

const getFields = async () => {
  const { data } = await getFieldsApi()
  fields.value = data
}

onMounted(() => {
  getCountriesData()
  getLanguagesData()
  getFields()
})
</script>

<style scoped>
.search-page {
  max-width: 1200px;
  margin: 0 auto;
  padding: 20px;
}

.search-conditions,
.search-results {
  margin-bottom: 20px;
}

.card-title {
  font-size: 1.5rem;
  color: #409eff;
  margin: 0;
}

.global-search,
.custom-conditions,
.add-condition,
.search-button {
  margin-bottom: 20px;
}

.select-item,
.input-item {
  margin-right: 10px;
  width: 200px;
}

.condition-item {
  display: flex;
  align-items: center;
  margin-bottom: 10px;
}

.add-condition {
  display: flex;
  justify-content: flex-start;
}
</style>
