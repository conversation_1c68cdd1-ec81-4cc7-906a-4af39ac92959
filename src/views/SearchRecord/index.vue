<script setup lang="tsx">
import { reactive, ref, unref } from 'vue'
import { createSearchRecordApi, getSearchRecordApi } from '@/api/searchRecord'
import { useTable } from '@/hooks/web/useTable'
import { Table } from '@/components/Table'
import { Search } from '@/components/Search'
import { ContentWrap } from '@/components/ContentWrap'
import { CrudSchema, useCrudSchemas } from '@/hooks/web/useCrudSchemas'
import { BaseButton } from '@/components/Button'
import { useRouter } from 'vue-router'
import { ElTag } from 'element-plus'
// import { searchTypes } from '@/constants/searchType'
import { FormSchema } from '@/components/Form'
import { getAuthListAPI } from '@/api/user'
import { filterType, filterSource, filterSearchRecordType } from '@/utils/filter'

const { tableRegister, tableState, tableMethods } = useTable({
  fetchDataApi: async () => {
    const { pageSize, currentPage } = tableState
    const res = await getSearchRecordApi({
      page: unref(currentPage),
      size: unref(pageSize),
      ...unref(searchParams)
    })
    return {
      list: res.data.items || [],
      total: res.data.total
    }
  }
})

const { total, loading, dataList, pageSize, currentPage } = tableState
const { getList } = tableMethods

const crudSchemas = reactive<CrudSchema[]>([
  {
    field: 'selection',
    search: {
      hidden: true
    },
    form: {
      hidden: true
    },
    detail: {
      hidden: true
    },
    table: {
      type: 'selection'
    }
  },
  {
    field: 'index',
    label: '序号',
    type: 'index',
    search: {
      hidden: true
    }
  },
  {
    field: 'search_keyword',
    label: '搜索关键词'
  },
  {
    field: 'type',
    label: '搜索来源',
    search: {
      hidden: true
    },
    slots: {
      default: (data: any) => {
        return (
          <>
            <ElTag type={filterSearchRecordType(data.row.type, true)}>
              {filterSearchRecordType(data.row.type)}
            </ElTag>
          </>
        )
      }
    }
  },
  {
    field: 'es_type',
    label: '搜索类型',
    search: {
      hidden: true
    },
    slots: {
      default: (data: any) => {
        return (
          <>
            <ElTag type={filterType(data.row.es_type, true)}>{filterType(data.row.es_type)}</ElTag>
          </>
        )
      }
    }
  },
  {
    field: 'search_field',
    label: '搜索范围',
    search: {
      hidden: true
    },
    slots: {
      default: ({ row }) => {
        return row.type === 1 ? <ElTag type="primary">{row.search_field.name}</ElTag> : null
      }
    }
  },
  {
    field: 'countries',
    label: '国家/地区',
    minWidth: '100',
    search: {
      hidden: true
    },
    slots: {
      default: ({ row }) => {
        return row.countries.map((i) => <ElTag key={i.id}>{i.name}</ElTag>)
      }
    }
  },
  {
    field: 'languages',
    label: '语言',
    minWidth: '100',
    search: {
      hidden: true
    },
    slots: {
      default: ({ row }) => {
        return row.languages.map((i) => <ElTag key={i.id}>{i.name}</ElTag>)
      }
    }
  },
  {
    field: 'user.username',
    label: '搜索人',
    search: {
      hidden: true
    }
  },
  {
    field: 'created_at',
    label: '搜索时间',
    search: {
      hidden: true
    }
  },
  {
    field: 'action',
    label: '操作',
    width: 240,
    search: {
      hidden: true
    },
    slots: {
      default: (data: any) => {
        const row = data.row
        return (
          <>
            <BaseButton type="primary" onClick={() => searchTo(row)} size="small">
              再次搜索
            </BaseButton>
          </>
        )
      }
    }
  }
])

const { allSchemas } = useCrudSchemas(crudSchemas)

const searchSchemas = reactive<FormSchema[]>([
  {
    field: 'search_keyword',
    label: '搜索关键词',
    component: 'Input'
  },
  {
    field: 'user_id',
    label: '搜索人',
    component: 'Select',
    componentProps: {
      nodeKey: 'id',
      props: {
        value: 'id',
        label: 'name'
      }
    },
    optionApi: async () => {
      const { data } = await getAuthListAPI()
      return data
    }
  },
  {
    field: 'time',
    label: '搜索时间',
    component: 'DatePicker',
    componentProps: {
      type: 'datetimerange',
      valueFormat: 'YYYY-MM-DD HH:mm:ss'
    }
  }
])

const searchParams = ref({})
const setSearchParams = (data: any) => {
  searchParams.value = data
  getList()
}

const router = useRouter()

const searchTo = (row) => {
  const apiParams = {
    keyword: row.search_keyword,
    searchField: row.search_field ? row.search_field.key : null,
    esType: row.es_type,
    searchCountries: row.countries.map((i) => i.id),
    searchLanguages: row.languages.map((i) => i.id),
    type: row.type
  }
  if (row.type === 1) {
    // 记录日志
    createSearchRecordApi(apiParams)
  }

  const routeName =
    row.type === 2 ? 'PasswordModule' : row.type === 3 ? 'fileSearchModule' : 'Result'

  const resultTo = router.resolve({
    name: routeName,
    query: apiParams
  })

  window.open(resultTo.href, '_blank')
}
</script>

<template>
  <ContentWrap>
    <Search :schema="searchSchemas" @reset="setSearchParams" @search="setSearchParams" />
    <Table
      v-model:current-page="currentPage"
      v-model:page-size="pageSize"
      :columns="allSchemas.tableColumns"
      :data="dataList"
      :loading="loading"
      @register="tableRegister"
      :pagination="{
        total
      }"
    />
  </ContentWrap>
</template>
