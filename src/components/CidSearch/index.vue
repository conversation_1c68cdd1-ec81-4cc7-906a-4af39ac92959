<template>
  <div class="w-full">
    <ElRadioGroup v-model="esType" class="mb-4 flex justify-center items-center">
      <ElRadio :value="1" size="large">精确搜索</ElRadio>
      <ElRadio :value="5" size="large">模糊搜索</ElRadio>
    </ElRadioGroup>
    <ElInput v-model="keyword" placeholder="请输入关键词进行搜索" class="input-with-select">
      <template #prepend>
        <ElSelect
          multiple
          collapse-tags
          clearable=""
          v-model="searchType"
          placeholder="选择搜索类型"
          style="width: 150px; margin-right: 20px"
          size="large"
        >
          <ElOption
            v-for="(item, key) in formatSearchTypes"
            :key="key"
            :value="item.search_type"
            :label="item.name"
          />
        </ElSelect>
        <ElSelect
          multiple
          collapse-tags
          clearable=""
          v-model="searchCountries"
          placeholder="选择搜索国家/地区"
          style="width: 150px; margin-right: 20px"
          size="large"
        >
          <template #header>
            <ElInput
              placeholder="请输入关键字进行搜索"
              v-model="countryInput"
              @input="countryFilter"
          /></template>
          <ElOption
            v-for="(item, key) in countriesList"
            :key="key"
            :value="item.id"
            :label="`${item.name}${item.code}`"
          >
            <div class="flex justify-between">
              <span>{{ item.name }}</span>
              <span>{{ item.code }}</span>
            </div>
          </ElOption>
        </ElSelect>
        <ElSelect
          multiple
          collapse-tags
          clearable=""
          v-model="searchLanguages"
          placeholder="选择搜索语言"
          style="width: 150px"
          size="large"
        >
          <template #header>
            <ElInput
              placeholder="请输入关键字进行搜索"
              v-model="languageInput"
              @input="languageFilter"
          /></template>
          <ElOption
            v-for="(item, key) in languagesList"
            :key="key"
            :value="item.id"
            :label="item.name"
          />
        </ElSelect>
      </template>
    </ElInput>
  </div>
</template>

<script setup>
import { ElButton, ElInput, ElOption, ElRadio, ElRadioGroup, ElSelect } from 'element-plus'
import { computed, onMounted, ref, watch, watchEffect } from 'vue'
import { searchTypes } from '@/constants/searchType'
import { getCountryList, getLanguageList } from '@/utils/cache'

const props = defineProps({
  modelValue: {
    type: Object,
    default: null
  }
})

const emit = defineEmits(['update:modelValue'])

const esType = ref(1)
const keyword = ref('')
const searchType = ref([])
const searchCountries = ref([])
const searchLanguages = ref([])

const originCountriesList = ref([])
const originLanguagesList = ref([])

const originValue = ref({
  es_type: esType.value,
  keyword: keyword.value,
  search_type: searchType.value,
  countries: searchCountries.value,
  languages: searchLanguages.value
})

const getCountriesData = async () => {
  const data = await getCountryList()
  countriesList.value = data
  originCountriesList.value = data
}

const getLanguagesData = async () => {
  const data = await getLanguageList()
  languagesList.value = data
  originLanguagesList.value = data
}

const countriesList = ref([])
const languagesList = ref([])

const formatSearchTypes = computed(() => {
  return searchTypes.filter((item) => !item.hidden)
})

const countryInput = ref(null)
const countryFilter = (val) => {
  if (!val) {
    countriesList.value = originCountriesList.value
  } else {
    countriesList.value = countriesList.value.filter((item) => {
      return item.name.indexOf(val) > -1 || item.code.toUpperCase().indexOf(val.toUpperCase()) > -1
    })
  }
}

const languageInput = ref(null)
const languageFilter = (val) => {
  if (!val) {
    languagesList.value = originLanguagesList.value
  } else {
    languagesList.value = languagesList.value.filter((item) => {
      return item.name.indexOf(val) > -1
    })
  }
}

watchEffect(() => {
  originValue.value = {
    es_type: esType.value,
    keyword: keyword.value,
    search_type: searchType.value,
    countries: searchCountries.value,
    languages: searchLanguages.value
  }
})

watch(
  () => originValue.value,
  (val) => {
    emit('update:modelValue', val)
  },
  {
    deep: true
  }
)

onMounted(() => {
  getCountriesData()
  getLanguagesData()
})
</script>
