<script setup lang="ts">
import { useFullscreen } from '@vueuse/core'
import { propTypes } from '@/utils/propTypes'
import { useDesign } from '@/hooks/web/useDesign'
import { ElIcon } from 'element-plus'
import { FullScreen, Close } from '@element-plus/icons-vue'

const { getPrefixCls } = useDesign()

const prefixCls = getPrefixCls('screenfull')

defineProps({
  color: propTypes.string.def('')
})

const { toggle, isFullscreen } = useFullscreen()

const toggleFullscreen = () => {
  toggle()
}
</script>

<template>
  <div :class="prefixCls" @click="toggleFullscreen">
    <ElIcon :size="18" :color="color">
      <FullScreen v-if="!isFullscreen" />
      <Close v-else />
    </ElIcon>
  </div>
</template>
