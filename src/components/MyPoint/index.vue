<template>
  <ElTag type="success">积分：（{{ pointNumber }}）</ElTag>
</template>

<script lang="ts" setup>
import { getMyPointAPI } from '@/api/auth'
import { ElTag } from 'element-plus'
import { ref, watchEffect } from 'vue'

import { useRoute } from 'vue-router'
const pointNumber = ref(0)

const getPoint = async () => {
  const { data } = await getMyPointAPI()
  pointNumber.value = data
}

const route = useRoute()

watchEffect(() => {
  if (route.path) {
    getPoint()
  }
})
</script>

<style scoped></style>
