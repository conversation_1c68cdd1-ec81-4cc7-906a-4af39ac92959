<template>
  <el-select v-model="selectedValue" :placeholder="placeholder" :multiple="multiple">
    <template #header>
      <el-input placeholder="请输入关键字进行搜索" v-model="searchInput" @input="handleFilter" />
    </template>
    <el-option
      v-for="(item, key) in filteredList"
      :key="key"
      :value="item[dataKv.key]"
      :label="item[dataKv.val]"
    >
      <slot :item="item">
        {{ item[dataKv.val] }}
      </slot>
    </el-option>
  </el-select>
</template>

<script setup>
import { ElSelect, ElOption, ElInput } from 'element-plus'
import { ref, computed } from 'vue'

// 定义 props
const props = defineProps({
  modelValue: {
    type: [String, Number, Array],
    required: true
  },
  multiple: {
    type: Boolean,
    default: false
  },
  options: {
    type: Array,
    required: true
  },
  placeholder: {
    type: String,
    default: null
  },
  dataKv: {
    type: Object,
    default: () => {
      return {
        key: 'id',
        val: 'name'
      }
    }
  }
})

// 定义 emits
const emit = defineEmits(['update:modelValue'])

// 内部状态
const searchInput = ref('')
const selectedValue = computed({
  get() {
    return props.modelValue
  },
  set(value) {
    emit('update:modelValue', value)
  }
})

// 过滤后的列表
const filteredList = computed(() => {
  if (!searchInput.value) {
    return props.options
  }
  return props.options.filter((item) => {
    return (
      item.name.toLowerCase().includes(searchInput.value.toLowerCase()) ||
      item.code.toLowerCase().includes(searchInput.value.toLowerCase())
    )
  })
})

// 处理过滤逻辑
const handleFilter = (value) => {
  searchInput.value = value
}
</script>

<style scoped>
:deep(.el-select) {
  width: 100%;
}

:deep(.el-select__wrapper) {
  background-color: #4a4a4a;
}

:deep(.el-select-dropdown__item) {
  color: #ffffff;
}

:deep(.el-select-dropdown__item.hover) {
  background-color: #5a5a5a;
}

:deep(.el-input__wrapper) {
  background-color: #4a4a4a;
}

:deep(.el-input__inner) {
  color: #ffffff;
}
</style>
