import { ElSubMenu, ElMenuItem } from 'element-plus'
import { isUrl } from '@/utils/is'
import { useRenderMenuTitle } from './useRenderMenuTitle'
import { pathResolve } from '@/utils/routerHelper'

const { renderMenuTitle } = useRenderMenuTitle()

export const useRenderMenuItem = () => {
  const renderMenuItem = (routers: AppRouteRecordRaw[], parentPath = '/') => {
    return routers
      .filter((v) => !v.meta?.hidden)
      .map((v) => {
        const meta = v.meta ?? {}
        const fullPath = isUrl(v.path) ? v.path : pathResolve(parentPath, v.path)

        // 始终将 children 作为子菜单处理
        if (v.children && v.children.length > 0) {
          return (
            <ElSubMenu index={fullPath}>
              {{
                title: () => renderMenuTitle(meta),
                default: () => renderMenuItem(v.children!, fullPath)
              }}
            </ElSubMenu>
          )
        } else {
          // 如果没有 children，直接渲染为菜单项
          return (
            <ElMenuItem index={fullPath}>
              {{
                default: () => renderMenuTitle(meta)
              }}
            </ElMenuItem>
          )
        }
      })
  }

  return {
    renderMenuItem
  }
}
