<template>
  <div>
    <ElDrawer v-model="show" title="自动登录" size="50%">
      <ElAlert
        type="success"
        :description="`您已选中的密码数为：${selectedRows.length}，点击自动登录按钮即可自动登录。`"
        :closable="false"
      />
      <ElForm class="mt-3" :model="formData" :rules="rules" ref="formRef" label-position="top">
        <ElFormItem label="代理" prop="proxy">
          <ElSelect
            filterable
            v-model="formData.proxy"
            placeholder="请选择代理"
            class="mt-3"
            clearable
            prop="proxy"
          >
            <ElOption
              v-for="item in proxyList"
              :key="item.id"
              :label="item.name"
              :value="item.name"
            />
          </ElSelect>
        </ElFormItem>
        <ElFormItem label="备注" prop="notes">
          <ElInput v-model="formData.notes" clearable placeholder="请输入备注" class="mt-3" />
        </ElFormItem>
      </ElForm>

      <ElInput v-model="keyword" clearable placeholder="请输入关键字进行搜索" class="mt-3" />
      <ElTable v-loading="loading" class="mt-3" :data="formatTableData" border style="width: 100%">
        <ElTableColumn label="开启">
          <template #header>
            <ElSwitch v-model="switchValue" @change="handleSwitchAllChange" />
          </template>
          <template #default="{ row }">
            <ElSwitch
              v-model="row._source.checked"
              @change="(val) => handleSingleSwitchChange(val, row)"
            />
          </template>
        </ElTableColumn>
        <ElTableColumn prop="_source.url" label="站点" />
        <ElTableColumn prop="_source.username" label="用户名" />
        <ElTableColumn prop="_source.password" label="密码" />
      </ElTable>

      <template #footer>
        <div>
          <BaseButton type="primary" @click="confirm" :loading="btnLoading">自动登录</BaseButton>
        </div>
      </template>
    </ElDrawer>
  </div>
</template>

<script setup>
import { ref, defineExpose, computed, watch, nextTick, reactive, h } from 'vue'
import {
  ElDrawer,
  ElTable,
  ElTableColumn,
  ElAlert,
  ElSpace,
  ElInput,
  ElSelect,
  ElOption,
  ElSwitch,
  ElForm,
  ElFormItem,
  ElMessage,
  ElNotification
} from 'element-plus'
import { searchItemAPI } from '@/api/search'
import { getProxyListApi, postAiAttackApi } from '@/api/aiAttack'
import { useRouter } from 'vue-router'
const router = useRouter()

const show = ref(false)
const selectedRows = ref([]) // 存储选中的行
const tableData = ref([])
const extendItemList = ref([])
const cid = ref()
const loading = ref(false)
const btnLoading = ref(false)
const keyword = ref('')
const switchValue = ref(false)

const originalTableData = ref([]) // 新增：保存原始数据
const originalExtendItemList = ref([]) // 新增：保存原始数据

const proxyList = ref([])
const formRef = ref()

const formData = reactive({
  proxy: '',
  notes: ''
})

const rules = reactive({
  proxy: [{ required: true, message: '请选择代理', trigger: 'change' }],
  notes: [{ required: true, message: '请输入备注', trigger: 'blur' }]
})

// 处理顶部总开关变化
const handleSwitchAllChange = (val) => {
  // 只更新当前筛选后显示的数据的选中状态
  const currentData = formatTableData.value
  currentData.forEach((row) => {
    if (row._source) {
      row._source.checked = val
    }
  })

  // 更新 selectedRows
  if (val) {
    // 移除已存在的项，避免重复
    const newSelectedRows = currentData.filter((row) => row._source)
    selectedRows.value = Array.from(new Set([...selectedRows.value, ...newSelectedRows]))
  } else {
    // 只移除当前显示的项
    selectedRows.value = selectedRows.value.filter(
      (row) => !currentData.some((currentRow) => currentRow === row)
    )
  }
}

// 处理单个开关变化
const handleSingleSwitchChange = (val, row) => {
  if (val) {
    // 如果是选中，将该行添加到 selectedRows
    if (!selectedRows.value.includes(row)) {
      selectedRows.value.push(row)
    }
  } else {
    // 如果是取消选中，从 selectedRows 中移除该行
    const index = selectedRows.value.findIndex((item) => item === row)
    if (index !== -1) {
      selectedRows.value.splice(index, 1)
    }
  }

  // 检查当前筛选后的数据是否都被选中，更新顶部开关状态
  const currentData = formatTableData.value
  if (currentData.length > 0) {
    const allChecked = currentData.every((row) => row._source?.checked)
    switchValue.value = allChecked
  }
}

const formatTableData = computed(() => {
  // 如果没有关键字，直接返回原始数据
  if (!keyword.value?.trim()) {
    return [...originalExtendItemList.value, ...originalTableData.value]
  }

  // 有关键字时进行筛选
  const searchText = keyword.value.toLowerCase().trim()

  const filteredTableData = originalTableData.value.filter((item) => {
    if (!item?._source) return false

    const url = item._source.url?.toLowerCase() || ''
    const username = item._source.username?.toLowerCase() || ''
    const password = item._source.password?.toLowerCase() || ''

    return (
      url.includes(searchText) || username.includes(searchText) || password.includes(searchText)
    )
  })

  const filteredExtendItemList = originalExtendItemList.value.filter((item) => {
    if (!item?._source) return false

    const url = item._source.url?.toLowerCase() || ''
    const username = item._source.username?.toLowerCase() || ''
    const password = item._source.password?.toLowerCase() || ''

    return (
      url.includes(searchText) || username.includes(searchText) || password.includes(searchText)
    )
  })

  return [...filteredExtendItemList, ...filteredTableData]
})

// 添加对过滤数据的监听，更新顶部开关状态
watch(
  formatTableData,
  (newData) => {
    if (newData.length > 0) {
      const allChecked = newData.every((row) => row._source?.checked)
      switchValue.value = allChecked
    }
  },
  { immediate: true }
)

// 监听关键词变化
watch(keyword, () => {
  nextTick(() => {
    if (formatTableData.value.length > 0) {
      const allChecked = formatTableData.value.every((row) => row._source?.checked)
      switchValue.value = allChecked
    }
  })
})

// 选择项发生变化时的处理函数
const handleSelectionChange = (selection) => {
  selectedRows.value = selection
}

// 确认按钮点击处理函数
const confirm = async () => {
  try {
    const valid = await formRef.value.validate()
    if (!valid) return

    if (selectedRows.value.length === 0) {
      ElMessage.warning('请至少选择一条数据')
      return
    }

    const params = {
      proxy: formData.proxy,
      notes: formData.notes,
      items: selectedRows.value.map((item) => item._source)
    }

    btnLoading.value = true
    try {
      const { data } = await postAiAttackApi(params)
      show.value = false

      ElNotification({
        type: 'success',
        title: '操作成功',
        message: h('div', null, [
          '操作成功，您可以点击',
          h(
            'a',
            {
              style: {
                color: '#409eff',
                cursor: 'pointer'
              },
              onClick: () => {
                router.push('/ai/autoLogin')
              }
            },
            'AI中心'
          ),
          '来进行数据查看'
        ])
      })
    } catch (error) {
      console.error(error)
    } finally {
      btnLoading.value = false
    }
  } catch (error) {}
}

// 修改 getPasswordList 函数，为返回的数据添加 checked 属性
const getPasswordList = async () => {
  if (cid.value) {
    loading.value = true
    try {
      const { data } = await searchItemAPI({
        size: 1000,
        cid: cid.value,
        search_from: 2
      })
      const { items } = data || {}
      // 为每一项添加 checked 属性
      originalTableData.value =
        items?.map((item) => ({
          ...item,
          _source: {
            ...item._source,
            index_id: item._id,
            checked: false
          }
        })) || []
    } catch (error) {
      console.error(error)
    } finally {
      loading.value = false
    }
  }
}

const getProxyList = async () => {
  try {
    const { data } = await getProxyListApi()
    // 将对象转换为数组并格式化为select需要的格式
    proxyList.value =
      Object.entries(data.proxy).map(([id, name]) => ({
        id,
        name
      })) || []
  } catch (error) {
    console.error('获取代理列表出错:', error)
    proxyList.value = []
  }
}

const open = (extendItems = [], extenalCid = null) => {
  cid.value = extenalCid
  switchValue.value = false // 重置顶部开关状态
  selectedRows.value = [] // 重置选中行

  // 使用 Map 进行去重
  const uniqueMap = new Map()
  extendItems.forEach((item) => {
    const key = `${item._source.url}_${item._source.username}_${item._source.password}`
    if (!uniqueMap.has(key)) {
      uniqueMap.set(key, {
        _source: {
          index_id: item._id,
          ...item._source,
          checked: false
        }
      })
    }
  })

  // 将 Map 转换回数组
  const formattedExtendItems = Array.from(uniqueMap.values())
  console.log('formattedExtendItems', formattedExtendItems)
  originalExtendItemList.value = formattedExtendItems
  getProxyList()
  getPasswordList()
  show.value = true
}

defineExpose({
  open
})
</script>

<style lang="scss" scoped></style>
