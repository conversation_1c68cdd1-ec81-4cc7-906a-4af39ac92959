import { useI18n } from '@/hooks/web/useI18n'
import { useUserStoreWithOut } from '@/store/modules/user'
import { usePermissionStore } from '@/store/modules/permission'

const userStore = useUserStoreWithOut()
const permissionStore = usePermissionStore()
const { t } = useI18n()

const hasPermission = (value: string): boolean => {
  const isSuper = userStore.getUserInfo?.is_super
  if (isSuper === true) {
    return true
  }
  const permissionCodes = (permissionStore.getPermissionCodes || []) as string[]

  if (!value) {
    throw new Error(t('permission.hasPermission'))
  }
  if (permissionCodes.includes(value)) {
    return true
  }
  return false
}

export const hasPermi = (value: string) => {
  return hasPermission(value)
}
