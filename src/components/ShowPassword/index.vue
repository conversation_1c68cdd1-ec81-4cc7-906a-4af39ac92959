<template>
  <span>
    <ElButton
      class="mx-2"
      :icon="View"
      size="small"
      type="primary"
      :loading="loading"
      @click="showPassword(row)"
      >显示帐密</ElButton
    >
    <ElDrawer append-to-body v-model="dialogVisible" title="查看密码">
      <ElDescriptions :column="1" border>
        <ElDescriptionsItem label="URL">
          {{ passwordData.url }}
        </ElDescriptionsItem>
        <ElDescriptionsItem label="Domain">
          {{ passwordData.url_domain }}
        </ElDescriptionsItem>
        <ElDescriptionsItem label="用户名">
          {{ passwordData.username }}
        </ElDescriptionsItem>
        <ElDescriptionsItem label="密码">
          {{ passwordData.password }}
        </ElDescriptionsItem>
      </ElDescriptions>
    </ElDrawer>
  </span>
</template>

<script setup>
import { checkPasswordAPI, getPasswordAPI } from '@/api/password'
import { View } from '@element-plus/icons-vue'
import { ElMessageBox, ElButton, ElDescriptions, ElDescriptionsItem, ElDrawer } from 'element-plus'
import { Dialog } from '@/components/Dialog'
import { ref } from 'vue'

defineProps({
  row: {
    type: Object,
    default: () => {}
  }
})

const dialogVisible = ref(false)
const passwordData = ref({})
const loading = ref(false)

const showPassword = async (row) => {
  loading.value = true
  const { data } = await checkPasswordAPI({
    index: row._index,
    id: row._id
  })
  loading.value = false
  const { isExist, isExceed, message } = data || {}
  if (isExist || !isExceed) {
    getPassword(row)
    return
  }
  ElMessageBox.confirm(message, '提示', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning'
  })
    .then(() => {
      getPassword(row)
    })
    .catch(() => {})
}

const getPassword = async (row) => {
  loading.value = true
  const { data } = await getPasswordAPI({
    index: row._index,
    id: row._id
  })
  loading.value = false
  passwordData.value = data
  dialogVisible.value = true
}
</script>

<style lang="scss" scoped></style>
