<script setup lang="ts">
import { useTagsViewStore } from '@/store/modules/tagsView'
import { useAppStore } from '@/store/modules/app'
import { Footer } from '@/components/Footer'
import { computed } from 'vue'
import { ElConfigProvider } from 'element-plus' // 导入 ElConfigProvider
import zhCn from 'element-plus/es/locale/lang/zh-cn' // 导入中文语言包

const appStore = useAppStore()

const footer = computed(() => appStore.getFooter)

const tagsViewStore = useTagsViewStore()

const getCaches = computed((): string[] => {
  return tagsViewStore.getCachedViews
})
</script>

<template>
  <!-- 使用 ElConfigProvider 包裹整个内容 -->
  <ElConfigProvider :locale="zhCn">
    <section
      :class="[
        'flex-1 p-[var(--app-content-padding)] w-[calc(100%-var(--app-content-padding)-var(--app-content-padding))] bg-[var(--app-content-bg-color)] dark:bg-[var(--el-bg-color)]'
      ]"
    >
      <router-view>
        <template #default="{ Component, route }">
          <keep-alive :include="getCaches">
            <component :is="Component" :key="route.fullPath" />
          </keep-alive>
        </template>
      </router-view>
    </section>
    <!-- <Footer v-if="footer" /> -->
  </ElConfigProvider>
</template>
