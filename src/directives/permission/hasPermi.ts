import type { App, Directive, DirectiveBinding } from 'vue'
import { useI18n } from '@/hooks/web/useI18n'
import { useUserStoreWithOut } from '@/store/modules/user'
import { usePermissionStore } from '@/store/modules/permission'

const userStore = useUserStoreWithOut()
const permissionStore = usePermissionStore()

const { t } = useI18n()

const hasPermission = (value: string): boolean => {
  const isSuper = userStore.getUserInfo?.is_super
  if (isSuper === true) {
    return true
  }
  const permissionCodes = (permissionStore.getPermissionCodes || []) as string[]

  if (!value) {
    throw new Error(t('permission.hasPermission'))
  }
  if (permissionCodes.includes(value)) {
    return true
  }
  return false
}
function hasPermi(el: Element, binding: DirectiveBinding) {
  const value = binding.value

  const flag = hasPermission(value)
  if (!flag) {
    el.parentNode?.removeChild(el)
  }
}
const mounted = (el: Element, binding: DirectiveBinding<any>) => {
  hasPermi(el, binding)
}

const permiDirective: Directive = {
  mounted
}

export const setupPermissionDirective = (app: App<Element>) => {
  app.directive('hasPermi', permiDirective)
}

export default permiDirective
