# 环境
VITE_NODE_ENV=production

# 接口前缀
VITE_API_BASE_PATH=http://localhost:8686/api

# 打包路径
VITE_BASE_PATH=/dist-dev/

# 是否删除debugger
VITE_DROP_DEBUGGER=false

# 是否删除console.log
VITE_DROP_CONSOLE=false

# 是否sourcemap
VITE_SOURCEMAP=true

# 输出路径
VITE_OUT_DIR=dist-dev

# 标题
VITE_APP_TITLE=深海智能体

# 是否包分析
VITE_USE_BUNDLE_ANALYZER=false

# 是否全量引入element-plus样式
VITE_USE_ALL_ELEMENT_PLUS_STYLE=false

# 是否切割css
VITE_USE_CSS_SPLIT=true

# 是否使用在线图标
VITE_USE_ONLINE_ICON=true

# 是否隐藏全局设置按钮
VITE_HIDE_GLOBAL_SETTING=false
